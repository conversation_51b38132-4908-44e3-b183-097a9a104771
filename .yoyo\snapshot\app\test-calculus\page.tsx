"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { mathEngine } from "@/lib/math-engine"
import { nlpProcessor } from "@/lib/nlp-processor"

export default function TestCalculusPage() {
  const [results, setResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setResults(prev => [...prev, message])
  }

  const testDerivatives = async () => {
    addResult("=== Testing Derivatives ===")

    // First test math.js directly
    try {
      const { derivative, simplify } = await import('mathjs')
      const directResult = derivative("sin(x)", "x")
      addResult(`Direct math.js: derivative("sin(x)", "x") = ${directResult.toString()}`)
      addResult(`Direct result type: ${typeof directResult}`)
      addResult(`Direct result constructor: ${directResult.constructor.name}`)

      const simplified = simplify(directResult)
      addResult(`Simplified: ${simplified.toString()}`)

      // Test evaluation
      const evaluated = directResult.evaluate({x: 0})
      addResult(`Evaluated at x=0: ${evaluated}`)
    } catch (error) {
      addResult(`Direct math.js error: ${(error as Error).message}`)
    }

    // Test calculus engine directly
    try {
      const { calculusEngine } = await import('@/lib/calculus-engine')
      const calculusResult = await calculusEngine.calculateDerivative("sin(x)", "x")
      addResult(`Calculus engine result: ${calculusResult.result}`)
      addResult(`Calculus engine type: ${typeof calculusResult.result}`)
    } catch (error) {
      addResult(`Calculus engine error: ${(error as Error).message}`)
    }

    const tests = [
      "derivative of sin(x)",
      "differentiate x^2",
      "d/dx(cos(x))",
      "derivative of x^3 + 2x"
    ]

    for (const test of tests) {
      try {
        addResult(`Testing: "${test}"`)
        const intent = nlpProcessor.parseNaturalLanguage(test)
        addResult(`Intent: ${JSON.stringify(intent)}`)

        const result = await mathEngine.processIntent(intent)
        addResult(`Result value: ${JSON.stringify(result.value)}`)
        addResult(`Result formattedValue: ${result.formattedValue}`)
        addResult(`Result type: ${typeof result.value}`)
        addResult(`Steps: ${result.steps?.length || 0} steps`)
        addResult("✅ SUCCESS")
      } catch (error) {
        addResult(`❌ ERROR: ${(error as Error).message}`)
      }
      addResult("---")
    }
  }

  const testIntegrals = async () => {
    addResult("=== Testing Integrals ===")
    
    const tests = [
      "integral of x",
      "integrate x^2",
      "antiderivative of sin(x)"
    ]

    for (const test of tests) {
      try {
        addResult(`Testing: "${test}"`)
        const intent = nlpProcessor.parseNaturalLanguage(test)
        addResult(`Intent: ${JSON.stringify(intent)}`)
        
        const result = await mathEngine.processIntent(intent)
        addResult(`Result: ${result.formattedValue}`)
        addResult(`Steps: ${result.steps?.length || 0} steps`)
        addResult("✅ SUCCESS")
      } catch (error) {
        addResult(`❌ ERROR: ${(error as Error).message}`)
      }
      addResult("---")
    }
  }

  const testLimits = async () => {
    addResult("=== Testing Limits ===")
    
    const tests = [
      "limit of x as x approaches 0",
      "limit of sin(x)/x as x approaches 0"
    ]

    for (const test of tests) {
      try {
        addResult(`Testing: "${test}"`)
        const intent = nlpProcessor.parseNaturalLanguage(test)
        addResult(`Intent: ${JSON.stringify(intent)}`)
        
        const result = await mathEngine.processIntent(intent)
        addResult(`Result: ${result.formattedValue}`)
        addResult(`Steps: ${result.steps?.length || 0} steps`)
        addResult("✅ SUCCESS")
      } catch (error) {
        addResult(`❌ ERROR: ${(error as Error).message}`)
      }
      addResult("---")
    }
  }

  const clearResults = () => {
    setResults([])
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Calculus Operations Test</h1>
      
      <div className="flex gap-4">
        <Button onClick={testDerivatives}>Test Derivatives</Button>
        <Button onClick={testIntegrals}>Test Integrals</Button>
        <Button onClick={testLimits}>Test Limits</Button>
        <Button onClick={clearResults} variant="outline">Clear Results</Button>
      </div>

      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Test Results</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <div key={index} className="text-sm font-mono whitespace-pre-wrap">
              {result}
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
}
