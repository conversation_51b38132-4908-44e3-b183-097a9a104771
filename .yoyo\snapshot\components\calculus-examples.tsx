"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calculator, TrendingUp, BarChart3, Target } from "lucide-react"

interface CalculusExample {
  title: string
  input: string
  description: string
  category: "derivative" | "integral" | "limit" | "definiteIntegral"
  difficulty: "basic" | "intermediate" | "advanced"
}

interface CalculusExamplesProps {
  onExampleClick: (input: string) => void
}

export function CalculusExamples({ onExampleClick }: CalculusExamplesProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all")

  const examples: CalculusExample[] = [
    // Derivatives
    {
      title: "Basic Power Rule",
      input: "derivative of x^2",
      description: "Find the derivative using the power rule",
      category: "derivative",
      difficulty: "basic"
    },
    {
      title: "Polynomial Derivative",
      input: "differentiate 3x^3 + 2x^2 - 5x + 1",
      description: "Derivative of a polynomial function",
      category: "derivative",
      difficulty: "basic"
    },
    {
      title: "Trigonometric Derivative",
      input: "derivative of sin(x)",
      description: "Derivative of sine function",
      category: "derivative",
      difficulty: "basic"
    },
    {
      title: "Chain Rule",
      input: "d/dx(sin(2x))",
      description: "Apply the chain rule for composite functions",
      category: "derivative",
      difficulty: "intermediate"
    },
    {
      title: "Product Rule",
      input: "derivative of x*sin(x)",
      description: "Use the product rule for multiplication",
      category: "derivative",
      difficulty: "intermediate"
    },
    {
      title: "Exponential Derivative",
      input: "differentiate e^(x^2)",
      description: "Derivative of exponential with chain rule",
      category: "derivative",
      difficulty: "advanced"
    },

    // Integrals
    {
      title: "Basic Integration",
      input: "integral of x",
      description: "Simple power rule for integration",
      category: "integral",
      difficulty: "basic"
    },
    {
      title: "Polynomial Integration",
      input: "integrate x^3 + 2x",
      description: "Integrate a polynomial function",
      category: "integral",
      difficulty: "basic"
    },
    {
      title: "Trigonometric Integration",
      input: "integral of cos(x)",
      description: "Integrate cosine function",
      category: "integral",
      difficulty: "basic"
    },
    {
      title: "Antiderivative",
      input: "antiderivative of 2x + 3",
      description: "Find the antiderivative of a linear function",
      category: "integral",
      difficulty: "basic"
    },
    {
      title: "Exponential Integration",
      input: "integral of e^x",
      description: "Integrate exponential function",
      category: "integral",
      difficulty: "intermediate"
    },

    // Definite Integrals
    {
      title: "Simple Definite Integral",
      input: "integral of x from 0 to 2",
      description: "Evaluate a definite integral",
      category: "definiteIntegral",
      difficulty: "basic"
    },
    {
      title: "Area Under Curve",
      input: "integrate x^2 from 1 to 3",
      description: "Find area under parabola",
      category: "definiteIntegral",
      difficulty: "intermediate"
    },
    {
      title: "Trigonometric Area",
      input: "integral of sin(x) from 0 to π",
      description: "Area under sine curve",
      category: "definiteIntegral",
      difficulty: "intermediate"
    },

    // Limits
    {
      title: "Simple Limit",
      input: "limit of x as x approaches 2",
      description: "Basic limit evaluation",
      category: "limit",
      difficulty: "basic"
    },
    {
      title: "Standard Trigonometric Limit",
      input: "limit of sin(x)/x as x approaches 0",
      description: "Famous trigonometric limit",
      category: "limit",
      difficulty: "intermediate"
    },
    {
      title: "Indeterminate Form",
      input: "limit of (x^2 - 4)/(x - 2) as x approaches 2",
      description: "Resolve 0/0 indeterminate form",
      category: "limit",
      difficulty: "advanced"
    },
    {
      title: "Limit at Infinity",
      input: "limit of 1/x as x approaches infinity",
      description: "Limit approaching infinity",
      category: "limit",
      difficulty: "intermediate"
    }
  ]

  const categories = [
    { id: "all", name: "All Examples", icon: Calculator },
    { id: "derivative", name: "Derivatives", icon: TrendingUp },
    { id: "integral", name: "Integrals", icon: BarChart3 },
    { id: "definiteIntegral", name: "Definite Integrals", icon: BarChart3 },
    { id: "limit", name: "Limits", icon: Target }
  ]

  const filteredExamples = selectedCategory === "all" 
    ? examples 
    : examples.filter(example => example.category === selectedCategory)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "basic": return "bg-green-100 text-green-800"
      case "intermediate": return "bg-yellow-100 text-yellow-800"
      case "advanced": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "derivative": return <TrendingUp className="w-4 h-4" />
      case "integral": return <BarChart3 className="w-4 h-4" />
      case "definiteIntegral": return <BarChart3 className="w-4 h-4" />
      case "limit": return <Target className="w-4 h-4" />
      default: return <Calculator className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">🧮 Calculus Examples</h2>
        <p className="text-muted-foreground">
          Try these calculus problems to test derivatives, integrals, and limits
        </p>
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 justify-center">
        {categories.map((category) => {
          const Icon = category.icon
          return (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center gap-2"
            >
              <Icon className="w-4 h-4" />
              {category.name}
            </Button>
          )
        })}
      </div>

      {/* Examples Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredExamples.map((example, index) => (
          <Card key={index} className="p-4 hover:shadow-md transition-shadow">
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(example.category)}
                  <h3 className="font-semibold text-sm text-foreground">
                    {example.title}
                  </h3>
                </div>
                <Badge className={getDifficultyColor(example.difficulty)}>
                  {example.difficulty}
                </Badge>
              </div>

              {/* Description */}
              <p className="text-xs text-muted-foreground">
                {example.description}
              </p>

              {/* Input Expression */}
              <div className="bg-muted/30 p-2 rounded text-xs font-mono">
                {example.input}
              </div>

              {/* Try Button */}
              <Button
                size="sm"
                className="w-full"
                onClick={() => onExampleClick(example.input)}
              >
                Try This Example
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {filteredExamples.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No examples found for this category.</p>
        </div>
      )}

      {/* Quick Tips */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-2">💡 Quick Tips</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use natural language: "derivative of x^2" or "d/dx(x^2)"</li>
          <li>• For integrals: "integral of sin(x)" or "antiderivative of cos(x)"</li>
          <li>• For limits: "limit of x as x approaches 0"</li>
          <li>• For definite integrals: "integral of x^2 from 0 to 1"</li>
        </ul>
      </Card>
    </div>
  )
}
