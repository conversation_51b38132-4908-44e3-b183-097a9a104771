"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import {
  BookOpen,
  Copy,
  ChevronDown,
  ChevronRight,
  Calculator,
  MessageSquare,
  Percent,
  Zap,
  AlertTriangle,
  Lightbulb,
  Play,
  TrendingUp
} from "lucide-react"
import { CalculusExamples } from "./calculus-examples"

interface UserGuideProps {
  onExampleClick?: (example: string) => void
}

export function UserGuide({ onExampleClick }: UserGuideProps) {

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const handleExampleClick = (example: string) => {
    if (onExampleClick) {
      onExampleClick(example)
    }
  }

  const ExampleCard = ({ 
    title, 
    examples, 
    icon: Icon, 
    description 
  }: { 
    title: string
    examples: { input: string; output: string; description: string }[]
    icon: any
    description: string
  }) => (
    <Card className="p-4">
      <div className="flex items-center gap-2 mb-3">
        <Icon className="w-5 h-5 text-accent" />
        <h3 className="font-semibold text-foreground">{title}</h3>
      </div>
      <p className="text-sm text-muted-foreground mb-4">{description}</p>
      <div className="space-y-3">
        {examples.map((example, index) => (
          <div key={index} className="border rounded-lg p-3 bg-muted/30">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-foreground">
                {example.description}
              </span>
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => copyToClipboard(example.input)}
                  title="Copy to clipboard"
                >
                  <Copy className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => handleExampleClick(example.input)}
                  title="Try this example"
                >
                  <Play className="w-3 h-3" />
                </Button>
              </div>
            </div>
            <div className="space-y-1">
              <div className="font-mono text-sm bg-background p-2 rounded border">
                <span className="text-muted-foreground">Input: </span>
                <span className="text-foreground">{example.input}</span>
              </div>
              <div className="font-mono text-sm bg-accent/10 p-2 rounded border">
                <span className="text-muted-foreground">Output: </span>
                <span className="text-accent font-semibold">{example.output}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  )

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <BookOpen className="w-8 h-8 text-accent" />
          <h1 className="text-3xl font-bold text-foreground">User Guide</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Learn how to use the AI Calculator effectively. From basic arithmetic to complex mathematical expressions, 
          this guide will help you get the most out of your calculations.
        </p>
      </div>

      {/* Quick Start */}
      <Card className="p-6 bg-accent/5 border-accent/20">
        <div className="flex items-center gap-2 mb-4">
          <Lightbulb className="w-5 h-5 text-accent" />
          <h2 className="text-xl font-semibold text-foreground">Quick Start</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-2 rounded-lg bg-accent/10 flex items-center justify-center">
              <span className="text-2xl">🗣️</span>
            </div>
            <h3 className="font-medium mb-1">Natural Language</h3>
            <p className="text-sm text-muted-foreground">Ask questions like "What is 15% of 240?"</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-2 rounded-lg bg-accent/10 flex items-center justify-center">
              <span className="text-2xl">🧮</span>
            </div>
            <h3 className="font-medium mb-1">Mathematical</h3>
            <p className="text-sm text-muted-foreground">Enter expressions like "2 + 3 * 4"</p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-2 rounded-lg bg-accent/10 flex items-center justify-center">
              <span className="text-2xl">🔀</span>
            </div>
            <h3 className="font-medium mb-1">Mixed</h3>
            <p className="text-sm text-muted-foreground">Combine both: "What is sin(π/2)?"</p>
          </div>
        </div>
      </Card>

      {/* Main Guide Content */}
      <Tabs defaultValue="examples" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="calculus">Calculus</TabsTrigger>
          <TabsTrigger value="syntax">Syntax</TabsTrigger>
          <TabsTrigger value="functions">Functions</TabsTrigger>
          <TabsTrigger value="tips">Tips & Tricks</TabsTrigger>
        </TabsList>

        <TabsContent value="examples" className="space-y-6">
          <ExampleCard
            title="Basic Arithmetic"
            icon={Calculator}
            description="Simple mathematical operations with numbers"
            examples={[
              { input: "2 + 3", output: "5", description: "Addition" },
              { input: "10 - 4", output: "6", description: "Subtraction" },
              { input: "6 * 7", output: "42", description: "Multiplication" },
              { input: "15 / 3", output: "5", description: "Division" },
              { input: "2 + 3 * 4", output: "14", description: "Order of operations" },
              { input: "(2 + 3) * 4", output: "20", description: "Using parentheses" },
            ]}
          />

          <ExampleCard
            title="Natural Language Queries"
            icon={MessageSquare}
            description="Ask mathematical questions in plain English"
            examples={[
              { input: "What is 2 plus 3?", output: "5", description: "Natural addition" },
              { input: "What is the square root of 144?", output: "12", description: "Square root question" },
              { input: "Add 5 and 7", output: "12", description: "Addition command" },
              { input: "Multiply 8 by 9", output: "72", description: "Multiplication command" },
              { input: "What is 20 divided by 4?", output: "5", description: "Division question" },
            ]}
          />

          <ExampleCard
            title="Percentage Calculations"
            icon={Percent}
            description="Calculate percentages in various formats"
            examples={[
              { input: "15% of 240", output: "36", description: "Basic percentage" },
              { input: "What is 25% of 80?", output: "20", description: "Percentage question" },
              { input: "Calculate 10% of 150", output: "15", description: "Calculate command" },
              { input: "33.5% of 120", output: "40.2", description: "Decimal percentage" },
            ]}
          />

          <ExampleCard
            title="Advanced Functions"
            icon={Zap}
            description="Trigonometry, logarithms, and other mathematical functions"
            examples={[
              { input: "sin(pi/2)", output: "1", description: "Trigonometric function" },
              { input: "log(100)", output: "4.605...", description: "Natural logarithm" },
              { input: "sqrt(169)", output: "13", description: "Square root" },
              { input: "2^10", output: "1024", description: "Exponentiation" },
              { input: "abs(-5)", output: "5", description: "Absolute value" },
              { input: "max(5, 3, 8, 1)", output: "8", description: "Maximum value" },
              { input: "round(3.7)", output: "4", description: "Round to nearest integer" },
            ]}
          />

          <ExampleCard
            title="Complex Expressions"
            icon={Calculator}
            description="Multi-step calculations and nested functions"
            examples={[
              { input: "(2 + 3) * (4 - 1) / sqrt(9)", output: "5", description: "Complex arithmetic" },
              { input: "sin(pi/4) + cos(pi/4)", output: "1.414...", description: "Trigonometric sum" },
              { input: "log(exp(5))", output: "5", description: "Logarithm of exponential" },
              { input: "sqrt(sin(pi/2)^2 + cos(pi/2)^2)", output: "1", description: "Pythagorean identity" },
            ]}
          />

          <ExampleCard
            title="Word Problems"
            icon={MessageSquare}
            description="Real-world mathematical questions in natural language"
            examples={[
              { input: "If I have 20 apples and eat 25% of them, how many are left?", output: "15", description: "Percentage word problem" },
              { input: "What is the area of a circle with radius 5?", output: "78.54...", description: "Geometry calculation" },
              { input: "How much is 15% tip on a $80 bill?", output: "12", description: "Tip calculation" },
              { input: "What is 3 factorial?", output: "6", description: "Factorial calculation" },
            ]}
          />
        </TabsContent>

        <TabsContent value="calculus" className="space-y-4">
          <CalculusExamples onExampleClick={handleExampleClick} />
        </TabsContent>

        <TabsContent value="syntax" className="space-y-4">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4 text-foreground">Input Syntax Guide</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-2 text-foreground">Mathematical Operators</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {[
                    { symbol: "+", description: "Addition" },
                    { symbol: "-", description: "Subtraction" },
                    { symbol: "*", description: "Multiplication" },
                    { symbol: "/", description: "Division" },
                    { symbol: "^", description: "Exponentiation" },
                    { symbol: "()", description: "Parentheses" },
                    { symbol: "%", description: "Percentage" },
                    { symbol: "!", description: "Factorial" },
                  ].map((op, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                      <code className="font-mono font-bold text-accent">{op.symbol}</code>
                      <span className="text-sm text-muted-foreground">{op.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2 text-foreground">Constants</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {[
                    { symbol: "pi", value: "3.14159...", description: "Pi (π)" },
                    { symbol: "e", value: "2.71828...", description: "Euler's number" },
                    { symbol: "i", value: "√(-1)", description: "Imaginary unit" },
                  ].map((constant, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded">
                      <div>
                        <code className="font-mono font-bold text-accent">{constant.symbol}</code>
                        <span className="text-sm text-muted-foreground ml-2">{constant.description}</span>
                      </div>
                      <span className="text-sm font-mono text-muted-foreground">{constant.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="functions" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              {
                category: "Trigonometric",
                functions: ["sin(x)", "cos(x)", "tan(x)", "asin(x)", "acos(x)", "atan(x)"]
              },
              {
                category: "Logarithmic",
                functions: ["log(x)", "log10(x)", "log2(x)", "exp(x)"]
              },
              {
                category: "Power & Root",
                functions: ["sqrt(x)", "cbrt(x)", "pow(x,y)", "x^y"]
              },
              {
                category: "Utility",
                functions: ["abs(x)", "max(a,b,c)", "min(a,b,c)", "round(x)", "ceil(x)", "floor(x)"]
              }
            ].map((group, index) => (
              <Card key={index} className="p-4">
                <h3 className="font-semibold mb-3 text-foreground">{group.category} Functions</h3>
                <div className="space-y-2">
                  {group.functions.map((func, funcIndex) => (
                    <div key={funcIndex} className="flex items-center justify-between p-2 bg-muted/30 rounded">
                      <code className="font-mono text-sm text-accent">{func}</code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => copyToClipboard(func)}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="tips" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Lightbulb className="w-5 h-5 text-accent" />
                <h3 className="font-semibold text-foreground">Pro Tips</h3>
              </div>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Use Ctrl+Enter to quickly calculate</li>
                <li>• Click example buttons to auto-fill input</li>
                <li>• Watch the input type badge for feedback</li>
                <li>• Use parentheses to control order of operations</li>
                <li>• Try both natural language and math notation</li>
                <li>• Use the Play button (▶) to try examples instantly</li>
                <li>• Copy button copies expressions to clipboard</li>
                <li>• History saves all your calculations automatically</li>
              </ul>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <AlertTriangle className="w-5 h-5 text-yellow-500" />
                <h3 className="font-semibold text-foreground">Common Mistakes</h3>
              </div>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• Don't forget multiplication: use "2*x" not "2x"</li>
                <li>• Use "pi" instead of "π" symbol</li>
                <li>• Check parentheses are balanced</li>
                <li>• Use "log" for natural log, "log10" for base-10</li>
                <li>• Angles in trig functions are in radians</li>
                <li>• For percentages, use "15% of 240" format</li>
                <li>• Variables need to be defined (use numbers instead)</li>
              </ul>
            </Card>
          </div>

          {/* Interactive Quick Test Section */}
          <Card className="p-6 bg-accent/5 border-accent/20">
            <div className="flex items-center gap-2 mb-4">
              <Play className="w-5 h-5 text-accent" />
              <h3 className="text-lg font-semibold text-foreground">Quick Test - Try These Now!</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Click any example below to instantly try it in the calculator:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {[
                "2 + 3 * 4",
                "What is 15% of 240?",
                "sqrt(144)",
                "sin(pi/2)",
                "log(100)",
                "abs(-10)",
                "max(5, 3, 8, 1)",
                "What is the square root of 169?",
                "25% of 80"
              ].map((example, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="justify-start text-left h-auto p-3 text-xs hover:bg-accent/10"
                  onClick={() => handleExampleClick(example)}
                >
                  <Play className="w-3 h-3 mr-2 text-accent" />
                  {example}
                </Button>
              ))}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
