// Debug the complete calculus flow to find where the issue occurs
import { derivative, simplify } from 'mathjs';

console.log("=== Debugging Complete Calculus Flow ===\n");

// Step 1: Test math.js directly
console.log("1. Testing math.js directly:");
console.log("============================");

const expr = "sin(x)";
const variable = "x";

const derivResult = derivative(expr, variable);
console.log(`derivative("${expr}", "${variable}") =`, derivResult);
console.log(`Type:`, typeof derivResult);
console.log(`Constructor:`, derivResult.constructor.name);
console.log(`toString():`, derivResult.toString());

const simplified = simplify(derivResult);
console.log(`simplified =`, simplified);
console.log(`simplified.toString():`, simplified.toString());

// Step 2: Test evaluation
console.log("\n2. Testing evaluation:");
console.log("======================");

try {
  const evaluated = derivResult.evaluate({x: 0});
  console.log(`derivResult.evaluate({x: 0}) =`, evaluated);
} catch (error) {
  console.log(`Evaluation error:`, error.message);
}

try {
  const evaluatedSimplified = simplified.evaluate({x: 0});
  console.log(`simplified.evaluate({x: 0}) =`, evaluatedSimplified);
} catch (error) {
  console.log(`Simplified evaluation error:`, error.message);
}

// Step 3: Test if there's any automatic evaluation happening
console.log("\n3. Testing automatic evaluation:");
console.log("================================");

// Check if the result is being converted to a number somehow
const resultString = simplified.toString();
console.log(`resultString = "${resultString}"`);
console.log(`typeof resultString = ${typeof resultString}`);

// Check if there's any implicit evaluation
const resultValue = simplified.valueOf ? simplified.valueOf() : simplified;
console.log(`resultValue =`, resultValue);
console.log(`typeof resultValue = ${typeof resultValue}`);

// Step 4: Test with different expressions
console.log("\n4. Testing with x^2:");
console.log("====================");

const expr2 = "x^2";
const derivResult2 = derivative(expr2, variable);
const simplified2 = simplify(derivResult2);

console.log(`derivative("${expr2}", "${variable}") =`, derivResult2.toString());
console.log(`simplified =`, simplified2.toString());

try {
  const evaluated2 = simplified2.evaluate({x: 0});
  console.log(`evaluated at x=0 =`, evaluated2);
} catch (error) {
  console.log(`Evaluation error:`, error.message);
}

// Step 5: Test JSON serialization (might be causing issues)
console.log("\n5. Testing JSON serialization:");
console.log("==============================");

try {
  const jsonString = JSON.stringify(simplified);
  console.log(`JSON.stringify(simplified) =`, jsonString);
} catch (error) {
  console.log(`JSON serialization error:`, error.message);
}

// Step 6: Test if the issue is in the return value
console.log("\n6. Testing return value simulation:");
console.log("===================================");

const mockResult = {
  result: simplified.toString(),
  originalExpression: expr,
  operation: 'derivative',
  variable: variable,
  isExact: true
};

console.log(`Mock result:`, mockResult);
console.log(`Mock result.result:`, mockResult.result);
console.log(`typeof Mock result.result:`, typeof mockResult.result);

console.log("\n=== Debug Complete ===");
