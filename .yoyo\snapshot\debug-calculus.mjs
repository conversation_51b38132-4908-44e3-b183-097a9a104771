// Debug calculus operations to find the issue
import { derivative, simplify, evaluate } from 'mathjs';

console.log("=== Debugging Calculus Operations ===\n");

// Test math.js derivative directly
console.log("1. Testing math.js derivative function directly:");
console.log("===============================================");

try {
  const expr1 = "sin(x)";
  const result1 = derivative(expr1, "x");
  console.log(`derivative("${expr1}", "x") =`, result1.toString());
  
  const expr2 = "x^2";
  const result2 = derivative(expr2, "x");
  console.log(`derivative("${expr2}", "x") =`, result2.toString());
  
  const expr3 = "cos(x)";
  const result3 = derivative(expr3, "x");
  console.log(`derivative("${expr3}", "x") =`, result3.toString());
  
  const expr4 = "x^3 + 2*x";
  const result4 = derivative(expr4, "x");
  console.log(`derivative("${expr4}", "x") =`, result4.toString());
  
} catch (error) {
  console.log("Error with math.js derivative:", error.message);
}

console.log("\n2. Testing Algebrite integration:");
console.log("=================================");

try {
  // Dynamic import for Algebrite
  const AlgebriteModule = await import('algebrite');
  const Algebrite = AlgebriteModule.default;

  console.log("Algebrite loaded successfully");
  console.log("Available methods:", Object.keys(Algebrite));

  // Test using eval method
  const derivResult = Algebrite.eval('derivative(sin(x), x)');
  console.log(`Algebrite.eval('derivative(sin(x), x)') =`, derivResult.toString());

  // Test integral using eval
  const integralResult = Algebrite.eval('integral(x^2, x)');
  console.log(`Algebrite.eval('integral(x^2, x)') =`, integralResult.toString());

  // Test limit using eval
  const limitResult = Algebrite.eval('limit(sin(x)/x, x, 0)');
  console.log(`Algebrite.eval('limit(sin(x)/x, x, 0)') =`, limitResult.toString());

  // Test using specific methods
  const x = Algebrite.eval('x');
  const sinx = Algebrite.eval('sin(x)');
  const integralMethod = Algebrite.integral(Algebrite.eval('x^2'), x);
  console.log(`Algebrite.integral(x^2, x) =`, integralMethod.toString());

} catch (error) {
  console.log("Error with Algebrite:", error.message);
}

console.log("\n3. Testing expression evaluation:");
console.log("=================================");

try {
  // Test if expressions are being evaluated instead of kept symbolic
  const testExpr = "sin(0)";
  const evalResult = evaluate(testExpr);
  console.log(`evaluate("${testExpr}") =`, evalResult);
  
  const testExpr2 = "cos(0)";
  const evalResult2 = evaluate(testExpr2);
  console.log(`evaluate("${testExpr2}") =`, evalResult2);
  
} catch (error) {
  console.log("Error with evaluation:", error.message);
}

console.log("\n4. Testing variable substitution:");
console.log("=================================");

try {
  // Check if the issue is with variable substitution
  const expr = derivative("sin(x)", "x");
  console.log("Raw derivative result:", expr);
  console.log("Type of result:", typeof expr);
  console.log("Result toString():", expr.toString());
  
  // Try evaluating with x = 0
  const evaluated = expr.evaluate({x: 0});
  console.log("Evaluated at x=0:", evaluated);
  
} catch (error) {
  console.log("Error with variable substitution:", error.message);
}

console.log("\n=== Debug Complete ===");
