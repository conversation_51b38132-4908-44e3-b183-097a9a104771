import { derivative, simplify, parse } from 'mathjs'

// Import Algebrite for integration and limits
// Note: We'll use dynamic import to handle potential SSR issues
let Algebrite: any = null

// Initialize Algebrite
async function initAlgebrite() {
  if (typeof window !== 'undefined' && !Algebrite) {
    try {
      const AlgebriteModule = await import('algebrite')
      Algebrite = AlgebriteModule.default
    } catch (error) {
      console.warn('Failed to load Algebrite:', error)
    }
  }
}

export interface CalculusResult {
  result: string
  steps: CalculusStep[]
  originalExpression: string
  operation: 'derivative' | 'integral' | 'limit'
  variable?: string
  isExact: boolean
  alternativeFormats?: string[]
}

export interface CalculusStep {
  step: number
  description: string
  expression: string
  rule?: string
}

export interface LimitOptions {
  variable: string
  approaches: string | number
  direction?: 'left' | 'right' | 'both'
}

export class CalculusEngine {
  constructor() {
    // Initialize Algebrite when the engine is created
    initAlgebrite()
  }

  /**
   * Calculate the derivative of an expression
   */
  async calculateDerivative(expression: string, variable: string = 'x'): Promise<CalculusResult> {
    try {
      // Use math.js for derivative calculation
      const derivativeResult = derivative(expression, variable)
      const simplified = simplify(derivativeResult)
      
      // Generate step-by-step solution
      const steps = this.generateDerivativeSteps(expression, variable, simplified.toString())
      
      const symbolicResult = simplified.toString()

      return {
        result: symbolicResult,
        steps,
        originalExpression: expression,
        operation: 'derivative',
        variable,
        isExact: true,
        alternativeFormats: [
          symbolicResult,
          `d/d${variable}(${expression})`,
          `f'(${variable}) = ${symbolicResult}`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate derivative: ${(error as Error).message}`)
    }
  }

  /**
   * Calculate the integral of an expression
   */
  async calculateIntegral(expression: string, variable: string = 'x', definite?: { lower: string, upper: string }): Promise<CalculusResult> {
    try {
      await initAlgebrite()

      if (!Algebrite) {
        throw new Error('Integration engine not available')
      }

      let result: string
      let steps: CalculusStep[]

      if (definite) {
        // Definite integral using Algebrite.eval
        const integralExpression = `defint(${expression}, ${variable}, ${definite.lower}, ${definite.upper})`
        const evalResult = Algebrite.eval(integralExpression)
        result = evalResult.toString()
        steps = this.generateDefiniteIntegralSteps(expression, variable, definite.lower, definite.upper, result)
      } else {
        // Indefinite integral using Algebrite.eval
        const integralExpression = `integral(${expression}, ${variable})`
        const evalResult = Algebrite.eval(integralExpression)
        result = evalResult.toString()
        steps = this.generateIndefiniteIntegralSteps(expression, variable, result)
      }

      return {
        result: result + (definite ? '' : ' + C'),
        steps,
        originalExpression: expression,
        operation: 'integral',
        variable,
        isExact: true,
        alternativeFormats: [
          result + (definite ? '' : ' + C'),
          definite
            ? `∫[${definite.lower} to ${definite.upper}] ${expression} d${variable}`
            : `∫ ${expression} d${variable}`,
          definite ? result : `F(${variable}) = ${result} + C`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate integral: ${(error as Error).message}`)
    }
  }

  /**
   * Calculate the limit of an expression
   */
  async calculateLimit(expression: string, options: LimitOptions): Promise<CalculusResult> {
    try {
      await initAlgebrite()

      if (!Algebrite) {
        throw new Error('Limit calculation engine not available')
      }

      // Use Algebrite for limit calculation
      const limitExpression = `limit(${expression}, ${options.variable}, ${options.approaches})`
      const evalResult = Algebrite.eval(limitExpression)
      const result = evalResult.toString()

      const steps = this.generateLimitSteps(expression, options, result)

      return {
        result,
        steps,
        originalExpression: expression,
        operation: 'limit',
        variable: options.variable,
        isExact: true,
        alternativeFormats: [
          result,
          `lim[${options.variable}→${options.approaches}] ${expression}`,
          `L = ${result}`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate limit: ${(error as Error).message}`)
    }
  }

  /**
   * Generate step-by-step solution for derivatives
   */
  private generateDerivativeSteps(expression: string, variable: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Find the derivative of ${expression} with respect to ${variable}`,
      expression: `d/d${variable}(${expression})`,
      rule: 'Differentiation'
    })

    // Analyze the expression to determine which rules apply
    const expr = expression.toLowerCase()
    
    if (expr.includes('^')) {
      steps.push({
        step: 2,
        description: 'Apply the power rule: d/dx(x^n) = n·x^(n-1)',
        expression: `Using power rule`,
        rule: 'Power Rule'
      })
    }
    
    if (expr.includes('sin') || expr.includes('cos') || expr.includes('tan')) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply trigonometric differentiation rules',
        expression: `Using trigonometric rules`,
        rule: 'Trigonometric Rules'
      })
    }
    
    if (expr.includes('*') && expr.split('*').length === 2) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply the product rule: d/dx(uv) = u\'v + uv\'',
        expression: `Using product rule`,
        rule: 'Product Rule'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Simplify the result',
      expression: result,
      rule: 'Simplification'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for indefinite integrals
   */
  private generateIndefiniteIntegralSteps(expression: string, variable: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Find the indefinite integral of ${expression} with respect to ${variable}`,
      expression: `∫ ${expression} d${variable}`,
      rule: 'Integration'
    })

    // Analyze the expression to determine which rules apply
    const expr = expression.toLowerCase()
    
    if (expr.includes('^')) {
      steps.push({
        step: 2,
        description: 'Apply the power rule for integration: ∫x^n dx = x^(n+1)/(n+1) + C',
        expression: `Using power rule for integration`,
        rule: 'Power Rule for Integration'
      })
    }
    
    if (expr.includes('sin') || expr.includes('cos')) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply trigonometric integration rules',
        expression: `Using trigonometric integration`,
        rule: 'Trigonometric Integration'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Add the constant of integration',
      expression: result + ' + C',
      rule: 'Constant of Integration'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for definite integrals
   */
  private generateDefiniteIntegralSteps(expression: string, variable: string, lower: string, upper: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Evaluate the definite integral from ${lower} to ${upper}`,
      expression: `∫[${lower} to ${upper}] ${expression} d${variable}`,
      rule: 'Definite Integration'
    })

    steps.push({
      step: 2,
      description: 'Find the antiderivative',
      expression: `Find F(${variable}) such that F'(${variable}) = ${expression}`,
      rule: 'Antiderivative'
    })

    steps.push({
      step: 3,
      description: 'Apply the Fundamental Theorem of Calculus',
      expression: `F(${upper}) - F(${lower})`,
      rule: 'Fundamental Theorem of Calculus'
    })

    steps.push({
      step: 4,
      description: 'Evaluate and simplify',
      expression: result,
      rule: 'Evaluation'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for limits
   */
  private generateLimitSteps(expression: string, options: LimitOptions, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Evaluate the limit as ${options.variable} approaches ${options.approaches}`,
      expression: `lim[${options.variable}→${options.approaches}] ${expression}`,
      rule: 'Limit Evaluation'
    })

    // Check for common limit scenarios
    if (options.approaches === '0' && expression.includes('/')) {
      steps.push({
        step: 2,
        description: 'Check for indeterminate form (0/0)',
        expression: 'Substitute the limit value',
        rule: 'Indeterminate Form Check'
      })
    }

    if (expression.includes('sin') && options.approaches === '0') {
      steps.push({
        step: steps.length + 1,
        description: 'Apply the standard limit: lim[x→0] sin(x)/x = 1',
        expression: 'Using standard trigonometric limit',
        rule: 'Standard Trigonometric Limit'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Final result',
      expression: result,
      rule: 'Result'
    })

    return steps
  }

  /**
   * Validate calculus expression
   */
  validateCalculusExpression(expression: string, variable: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    try {
      // Try to parse the expression
      parse(expression)
    } catch (error) {
      errors.push(`Invalid expression syntax: ${(error as Error).message}`)
    }

    // Check if variable is present in expression
    if (!expression.includes(variable)) {
      errors.push(`Variable '${variable}' not found in expression`)
    }

    // Check for unsupported functions
    const unsupportedFunctions = ['factorial', 'gamma', 'beta']
    for (const func of unsupportedFunctions) {
      if (expression.includes(func)) {
        errors.push(`Function '${func}' is not supported in calculus operations`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Export singleton instance
export const calculusEngine = new CalculusEngine()
