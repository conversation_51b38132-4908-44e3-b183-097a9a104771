// History management with IndexedDB for persistent storage

import type { Calculation, HistoryFilter } from "./types"

export class HistoryManager {
  private dbName = "ai-calculator-db"
  private dbVersion = 1
  private storeName = "calculations"
  private db: IDBDatabase | null = null

  /**
   * Initialize the IndexedDB database
   */
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)

      request.onerror = () => {
        reject(new Error("Failed to open database"))
      }

      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        // Create object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: "id" })

          // Create indexes for efficient querying
          store.createIndex("timestamp", "timestamp", { unique: false })
          store.createIndex("inputType", "inputType", { unique: false })
          store.createIndex("complexity", "complexity", { unique: false })
        }
      }
    })
  }

  /**
   * Save a calculation to history
   */
  async saveCalculation(calculation: Calculation): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"))
        return
      }

      const transaction = this.db.transaction([this.storeName], "readwrite")
      const store = transaction.objectStore(this.storeName)

      const request = store.put(calculation)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error("Failed to save calculation"))
    })
  }

  /**
   * Get calculation history with optional filtering
   */
  async getHistory(filter?: HistoryFilter): Promise<Calculation[]> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"))
        return
      }

      const transaction = this.db.transaction([this.storeName], "readonly")
      const store = transaction.objectStore(this.storeName)
      const index = store.index("timestamp")

      // Get all records in reverse chronological order
      const request = index.openCursor(null, "prev")
      const results: Calculation[] = []

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result

        if (cursor) {
          const calculation = cursor.value as Calculation

          // Apply filters
          if (this.matchesFilter(calculation, filter)) {
            results.push(calculation)
          }

          cursor.continue()
        } else {
          resolve(results)
        }
      }

      request.onerror = () => reject(new Error("Failed to retrieve history"))
    })
  }

  /**
   * Search calculation history by query
   */
  async searchHistory(query: string): Promise<Calculation[]> {
    const allCalculations = await this.getHistory()
    const searchTerm = query.toLowerCase()

    return allCalculations.filter(
      (calc) =>
        calc.input.toLowerCase().includes(searchTerm) ||
        calc.result.formattedValue.toLowerCase().includes(searchTerm) ||
        calc.tags.some((tag) => tag.toLowerCase().includes(searchTerm)),
    )
  }

  /**
   * Get calculation by ID
   */
  async getCalculationById(id: string): Promise<Calculation | null> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"))
        return
      }

      const transaction = this.db.transaction([this.storeName], "readonly")
      const store = transaction.objectStore(this.storeName)
      const request = store.get(id)

      request.onsuccess = () => {
        resolve(request.result || null)
      }

      request.onerror = () => reject(new Error("Failed to get calculation"))
    })
  }

  /**
   * Delete a calculation from history
   */
  async deleteCalculation(id: string): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"))
        return
      }

      const transaction = this.db.transaction([this.storeName], "readwrite")
      const store = transaction.objectStore(this.storeName)
      const request = store.delete(id)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error("Failed to delete calculation"))
    })
  }

  /**
   * Clear all calculation history
   */
  async clearHistory(): Promise<void> {
    if (!this.db) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"))
        return
      }

      const transaction = this.db.transaction([this.storeName], "readwrite")
      const store = transaction.objectStore(this.storeName)
      const request = store.clear()

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error("Failed to clear history"))
    })
  }

  /**
   * Export history as JSON string
   */
  async exportHistory(): Promise<string> {
    const history = await this.getHistory()
    return JSON.stringify(history, null, 2)
  }

  /**
   * Import history from JSON string
   */
  async importHistory(jsonData: string): Promise<void> {
    try {
      const calculations = JSON.parse(jsonData) as Calculation[]

      for (const calc of calculations) {
        // Convert timestamp string back to Date object if needed
        if (typeof calc.timestamp === "string") {
          calc.timestamp = new Date(calc.timestamp)
        }
        await this.saveCalculation(calc)
      }
    } catch (error) {
      throw new Error("Invalid history data format")
    }
  }

  /**
   * Get statistics about calculation history
   */
  async getStatistics(): Promise<{
    totalCalculations: number
    naturalLanguageCount: number
    mathematicalCount: number
    complexityBreakdown: Record<string, number>
    recentActivity: { date: string; count: number }[]
  }> {
    const history = await this.getHistory()

    const stats = {
      totalCalculations: history.length,
      naturalLanguageCount: history.filter((c) => c.inputType === "natural").length,
      mathematicalCount: history.filter((c) => c.inputType === "mathematical").length,
      complexityBreakdown: {} as Record<string, number>,
      recentActivity: [] as { date: string; count: number }[],
    }

    // Count by complexity
    history.forEach((calc) => {
      const complexity = this.getCalculationComplexity(calc)
      stats.complexityBreakdown[complexity] = (stats.complexityBreakdown[complexity] || 0) + 1
    })

    // Recent activity (last 7 days)
    const now = new Date()
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now)
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split("T")[0]

      const count = history.filter((calc) => {
        const calcDate = calc.timestamp.toISOString().split("T")[0]
        return calcDate === dateStr
      }).length

      stats.recentActivity.push({ date: dateStr, count })
    }

    return stats
  }

  // Private helper methods
  private matchesFilter(calculation: Calculation, filter?: HistoryFilter): boolean {
    if (!filter) return true

    // Date range filter
    if (filter.dateRange) {
      const calcTime = calculation.timestamp.getTime()
      const startTime = filter.dateRange.start.getTime()
      const endTime = filter.dateRange.end.getTime()

      if (calcTime < startTime || calcTime > endTime) {
        return false
      }
    }

    // Input type filter
    if (filter.inputType && calculation.inputType !== filter.inputType) {
      return false
    }

    // Complexity filter
    if (filter.complexity) {
      const complexity = this.getCalculationComplexity(calculation)
      if (complexity !== filter.complexity) {
        return false
      }
    }

    // Tags filter
    if (filter.tags && filter.tags.length > 0) {
      const hasMatchingTag = filter.tags.some((tag) => calculation.tags.includes(tag))
      if (!hasMatchingTag) {
        return false
      }
    }

    return true
  }

  private getCalculationComplexity(calculation: Calculation): "basic" | "intermediate" | "advanced" {
    const input = calculation.input.toLowerCase()

    // Advanced: calculus, complex functions
    if (input.match(/derivative|integral|limit|sin|cos|tan|log|ln|sqrt|solve.*=.*0/)) {
      return "advanced"
    }

    // Intermediate: multiple operations, variables
    if (input.match(/[+\-*/]{2,}|[a-zA-Z]|\^|\(/)) {
      return "intermediate"
    }

    // Basic: simple arithmetic
    return "basic"
  }
}

// Export singleton instance
export const historyManager = new HistoryManager()
