// Core data types for the AI-powered calculator

export interface Calculation {
  id: string
  input: string
  inputType: "natural" | "mathematical" | "mixed"
  result: CalculationResult
  steps: Step[]
  timestamp: Date
  tags: string[]
}

export interface CalculationResult {
  value: string | number
  formattedValue: string
  unit?: string
  isExact: boolean
  alternativeFormats?: string[]
  steps?: Step[]
}

export interface Step {
  id: string
  description: string
  expression: string
  rule: string
  explanation: string
}

export interface MathematicalIntent {
  operation: "solve" | "simplify" | "differentiate" | "integrate" | "definiteIntegrate" | "limit" | "evaluate"
  expression: string
  variables?: string[]
  constraints?: string[]
  bounds?: {
    lower: string
    upper: string
  }
  approaches?: string
}

export interface ParsedExpression {
  original: string
  normalized: string
  tokens: Token[]
  type: ExpressionType
  complexity: "basic" | "intermediate" | "advanced"
}

export interface Token {
  type: "number" | "operator" | "function" | "variable" | "parenthesis"
  value: string
  position: number
}

export type ExpressionType = "arithmetic" | "algebraic" | "trigonometric" | "logarithmic" | "calculus" | "statistical"

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface MathEntity {
  type: "number" | "operation" | "function" | "variable"
  value: string
  confidence: number
}

export interface Solution {
  value: string | number
  steps: Step[]
  method: string
}

// Error handling types
export interface InputError extends Error {
  type: "syntax" | "ambiguous" | "unsupported"
  suggestions?: string[]
}

export interface ComputationError extends Error {
  type: "domain" | "overflow" | "division_by_zero" | "convergence"
  context?: string
}

export interface AIServiceError extends Error {
  type: "network" | "rate_limit" | "service_unavailable"
  retryAfter?: number
}

export interface RecoveryAction {
  type: "retry" | "fallback" | "clarification"
  message: string
  suggestedActions: string[]
}

export interface HistoryFilter {
  dateRange?: {
    start: Date
    end: Date
  }
  inputType?: "natural" | "mathematical" | "mixed"
  complexity?: "basic" | "intermediate" | "advanced"
  tags?: string[]
}
