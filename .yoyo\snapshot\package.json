{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:calculus": "vitest run tests/calculus/", "test:calculus:watch": "vitest tests/calculus/", "test:calculus:ui": "vitest --ui tests/calculus/", "test:calculus:comprehensive": "tsx scripts/test-calculus.ts", "test:derivatives": "vitest run tests/calculus/derivatives.test.ts", "test:integrals": "vitest run tests/calculus/integrals.test.ts", "test:limits": "vitest run tests/calculus/limits.test.ts", "test:symbolic": "vitest run tests/calculus/symbolic-preservation.test.ts", "test:errors": "vitest run tests/calculus/error-handling.test.ts", "test:nlp": "vitest run tests/calculus/nlp-integration.test.ts"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@vercel/analytics": "1.3.1", "algebrite": "^1.4.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "geist": "^1.3.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "mathjs": "latest", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.9", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "postcss": "^8.5", "tailwindcss": "^4.1.9", "tsx": "^4.20.5", "tw-animate-css": "1.3.3", "typescript": "^5", "vitest": "^3.2.4"}}