// Simple test to verify calculus operations work correctly
import { derivative } from 'mathjs';

console.log("=== Simple Calculus Test ===\n");

// Test 1: Math.js derivative
console.log("1. Testing Math.js derivative:");
const result1 = derivative("sin(x)", "x");
console.log(`derivative("sin(x)", "x") = ${result1.toString()}`);

// Test 2: Evaluate at specific point
console.log("\n2. Testing evaluation at x=0:");
const evaluated = result1.evaluate({x: 0});
console.log(`cos(x) evaluated at x=0 = ${evaluated}`);

// Test 3: Check if result is symbolic
console.log("\n3. Checking if result is symbolic:");
console.log(`Result type: ${typeof result1}`);
console.log(`Result constructor: ${result1.constructor.name}`);
console.log(`Is symbolic: ${result1.toString() !== result1.evaluate({x: 0}).toString()}`);

// Test 4: Algebrite
console.log("\n4. Testing Algebrite:");
try {
  const AlgebriteModule = await import('algebrite');
  const Algebrite = AlgebriteModule.default;
  
  const derivResult = Algebrite.eval('derivative(sin(x), x)');
  console.log(`Algebrite derivative: ${derivResult.toString()}`);
  
  const integralResult = Algebrite.eval('integral(x^2, x)');
  console.log(`Algebrite integral: ${integralResult.toString()}`);
  
} catch (error) {
  console.log(`Algebrite error: ${error.message}`);
}

console.log("\n=== Test Complete ===");
