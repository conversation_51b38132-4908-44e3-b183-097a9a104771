import { describe, it, expect } from 'vitest'

// Import all calculus test suites
import './derivatives.test'
import './integrals.test'
import './limits.test'
import './symbolic-preservation.test'
import './error-handling.test'
import './nlp-integration.test'

describe('Calculus Test Suite', () => {
  it('should have all calculus test modules loaded', () => {
    // This test ensures all calculus test files are properly imported
    expect(true).toBe(true)
  })
})

// Export test utilities for other test files
export const testUtils = {
  /**
   * Helper function to test symbolic preservation
   */
  expectSymbolic: (result: string, expectedPattern: RegExp, notExpected: string[]) => {
    expect(result).toMatch(expectedPattern)
    expect(typeof result).toBe('string')
    
    for (const notExp of notExpected) {
      expect(result).not.toBe(notExp)
    }
  },

  /**
   * Helper function to test calculus result structure
   */
  expectCalculusResult: (result: any, operation: string, variable: string) => {
    expect(result).toBeDefined()
    expect(result.operation).toBe(operation)
    expect(result.variable).toBe(variable)
    expect(result.isExact).toBe(true)
    expect(result.steps).toBeDefined()
    expect(Array.isArray(result.steps)).toBe(true)
    expect(result.alternativeFormats).toBeDefined()
    expect(Array.isArray(result.alternativeFormats)).toBe(true)
  },

  /**
   * Helper function to test NLP intent structure
   */
  expectNLPIntent: (intent: any, operation: string, expression: string, variables: string[]) => {
    expect(intent).toBeDefined()
    expect(intent.operation).toBe(operation)
    expect(intent.expression).toBe(expression)
    expect(intent.variables).toEqual(variables)
  },

  /**
   * Helper function to test step-by-step solutions
   */
  expectSteps: (steps: any[], minSteps: number = 1) => {
    expect(steps).toBeDefined()
    expect(Array.isArray(steps)).toBe(true)
    expect(steps.length).toBeGreaterThanOrEqual(minSteps)
    
    for (const step of steps) {
      expect(step.id).toBeDefined()
      expect(step.description).toBeDefined()
      expect(step.expression).toBeDefined()
      expect(typeof step.description).toBe('string')
      expect(typeof step.expression).toBe('string')
    }
  },

  /**
   * Helper function to test error handling
   */
  expectError: async (asyncFn: () => Promise<any>, errorMessage?: string) => {
    await expect(asyncFn).rejects.toThrow()
    
    if (errorMessage) {
      try {
        await asyncFn()
      } catch (error) {
        expect(error.message).toContain(errorMessage)
      }
    }
  },

  /**
   * Common test expressions for calculus operations
   */
  testExpressions: {
    derivatives: [
      { expr: 'sin(x)', expected: 'cos(x)', description: 'Basic trigonometric' },
      { expr: 'cos(x)', expected: '-sin(x)', description: 'Basic trigonometric' },
      { expr: 'x^2', expected: '2 * x', description: 'Power rule' },
      { expr: 'x^3', expected: '3 * x^2', description: 'Power rule' },
      { expr: 'e^x', expected: 'e^x', description: 'Exponential' },
      { expr: 'ln(x)', expected: '1 / x', description: 'Natural logarithm' },
      { expr: 'x*sin(x)', expected: 'sin(x) + x * cos(x)', description: 'Product rule' },
    ],
    integrals: [
      { expr: 'x', expected: /x\^2.*\+ C/, description: 'Power rule' },
      { expr: 'x^2', expected: /x\^3.*\+ C/, description: 'Power rule' },
      { expr: 'sin(x)', expected: /-cos\(x\).*\+ C/, description: 'Trigonometric' },
      { expr: 'cos(x)', expected: /sin\(x\).*\+ C/, description: 'Trigonometric' },
      { expr: 'e^x', expected: /e\^x.*\+ C/, description: 'Exponential' },
    ],
    limits: [
      { expr: 'x', approaches: '0', expected: '0', description: 'Basic limit' },
      { expr: 'x^2', approaches: '3', expected: '9', description: 'Polynomial limit' },
      { expr: 'sin(x)/x', approaches: '0', expected: '1', description: 'Standard trigonometric limit' },
      { expr: '(x^2-1)/(x-1)', approaches: '1', expected: '2', description: 'Indeterminate form' },
    ],
  },

  /**
   * Common NLP test phrases
   */
  nlpPhrases: {
    derivatives: [
      'derivative of {expr}',
      'differentiate {expr}',
      'd/dx({expr})',
      'what is the derivative of {expr}',
      'find the derivative of {expr}',
      'derive {expr}',
    ],
    integrals: [
      'integral of {expr}',
      'integrate {expr}',
      'antiderivative of {expr}',
      'what is the integral of {expr}',
      'find the integral of {expr}',
      'find the antiderivative of {expr}',
    ],
    limits: [
      'limit of {expr} as x approaches {value}',
      'limit of {expr} as x goes to {value}',
      'what is the limit of {expr} as x approaches {value}',
      'find the limit of {expr} as x approaches {value}',
      'lim[x→{value}] {expr}',
    ],
  },
}
