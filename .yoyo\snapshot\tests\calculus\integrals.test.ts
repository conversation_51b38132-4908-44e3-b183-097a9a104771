import { describe, it, expect } from 'vitest'
import { calculusEngine } from '@/lib/calculus-engine'
import { mathEngine } from '@/lib/math-engine'
import { nlpProcessor } from '@/lib/nlp-processor'

describe('Integration Operations', () => {
  describe('Basic Indefinite Integrals', () => {
    it('should calculate integral of x correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x', 'x')
      
      expect(result.result).toContain('x^2')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
      expect(result.originalExpression).toBe('x')
    })

    it('should calculate integral of x^2 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x')
      
      expect(result.result).toContain('x^3')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of x^3 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x^3', 'x')
      
      expect(result.result).toContain('x^4')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of constant correctly', async () => {
      const result = await calculusEngine.calculateIntegral('5', 'x')
      
      expect(result.result).toContain('5*x')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of 1/x correctly', async () => {
      const result = await calculusEngine.calculateIntegral('1/x', 'x')
      
      expect(result.result).toContain('log')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Trigonometric Integrals', () => {
    it('should calculate integral of sin(x) correctly', async () => {
      const result = await calculusEngine.calculateIntegral('sin(x)', 'x')
      
      expect(result.result).toContain('cos(x)')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of cos(x) correctly', async () => {
      const result = await calculusEngine.calculateIntegral('cos(x)', 'x')
      
      expect(result.result).toContain('sin(x)')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of sec(x)^2 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('sec(x)^2', 'x')
      
      expect(result.result).toContain('tan(x)')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Exponential Integrals', () => {
    it('should calculate integral of e^x correctly', async () => {
      const result = await calculusEngine.calculateIntegral('e^x', 'x')
      
      expect(result.result).toContain('e^x')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of 2^x correctly', async () => {
      const result = await calculusEngine.calculateIntegral('2^x', 'x')
      
      expect(result.result).toContain('2^x')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Polynomial Integrals', () => {
    it('should calculate integral of polynomial x^3 + 2x correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x^3 + 2*x', 'x')
      
      expect(result.result).toContain('x^4')
      expect(result.result).toContain('x^2')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate integral of 3x^2 - 4x + 5 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('3*x^2 - 4*x + 5', 'x')
      
      expect(result.result).toContain('x^3')
      expect(result.result).toContain('x^2')
      expect(result.result).toContain('5*x')
      expect(result.result).toContain('+ C')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Definite Integrals', () => {
    it('should calculate definite integral of x from 0 to 1 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x', 'x', { lower: '0', upper: '1' })
      
      expect(result.result).toBe('1/2')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
      expect(result.originalExpression).toBe('x')
    })

    it('should calculate definite integral of x^2 from 0 to 2 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x', { lower: '0', upper: '2' })
      
      expect(result.result).toBe('8/3')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate definite integral of sin(x) from 0 to π correctly', async () => {
      const result = await calculusEngine.calculateIntegral('sin(x)', 'x', { lower: '0', upper: 'pi' })
      
      expect(result.result).toBe('2')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate definite integral of 1 from 0 to 5 correctly', async () => {
      const result = await calculusEngine.calculateIntegral('1', 'x', { lower: '0', upper: '5' })
      
      expect(result.result).toBe('5')
      expect(result.operation).toBe('integral')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Natural Language Input Processing', () => {
    it('should process "integral of x^2" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('integral of x^2')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('integrate')
      expect(intent.expression).toBe('x^2')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toContain('x^3')
      expect(result.formattedValue).toContain('+ C')
    })

    it('should process "integrate sin(x)" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('integrate sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('integrate')
      expect(intent.expression).toBe('sin(x)')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toContain('cos(x)')
      expect(result.formattedValue).toContain('+ C')
    })

    it('should process "antiderivative of cos(x)" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('antiderivative of cos(x)')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('integrate')
      expect(intent.expression).toBe('cos(x)')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toContain('sin(x)')
      expect(result.formattedValue).toContain('+ C')
    })

    it('should process "what is the integral of e^x" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('what is the integral of e^x')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('integrate')
      expect(intent.expression).toBe('e^x')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toContain('e^x')
      expect(result.formattedValue).toContain('+ C')
    })

    it('should process "integral of x from 0 to 2" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('integral of x from 0 to 2')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('definiteIntegrate')
      expect(intent.expression).toBe('x')
      expect(intent.variables).toEqual(['x'])
      expect(intent.bounds).toEqual({ lower: '0', upper: '2' })
      expect(result.formattedValue).toBe('2')
    })

    it('should process "integrate x^2 from 1 to 3" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('integrate x^2 from 1 to 3')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('definiteIntegrate')
      expect(intent.expression).toBe('x^2')
      expect(intent.variables).toEqual(['x'])
      expect(intent.bounds).toEqual({ lower: '1', upper: '3' })
      expect(result.formattedValue).toBe('26/3')
    })
  })

  describe('Step-by-Step Solutions', () => {
    it('should provide step-by-step solution for basic integral', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x')
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('integral')
      expect(result.steps.some(step => step.rule === 'Power Rule for Integration')).toBe(true)
      expect(result.steps.some(step => step.rule === 'Constant of Integration')).toBe(true)
    })

    it('should provide step-by-step solution for trigonometric integral', async () => {
      const result = await calculusEngine.calculateIntegral('sin(x)', 'x')
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('integral')
      expect(result.steps.some(step => step.rule === 'Trigonometric Integration')).toBe(true)
    })

    it('should provide step-by-step solution for definite integral', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x', { lower: '0', upper: '1' })
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('definite integral')
      expect(result.steps.some(step => step.rule === 'Fundamental Theorem of Calculus')).toBe(true)
    })
  })

  describe('Alternative Formats', () => {
    it('should provide alternative formats for indefinite integral results', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x')
      
      expect(result.alternativeFormats).toBeDefined()
      expect(result.alternativeFormats.some(format => format.includes('∫'))).toBe(true)
      expect(result.alternativeFormats.some(format => format.includes('F(x)'))).toBe(true)
    })

    it('should provide alternative formats for definite integral results', async () => {
      const result = await calculusEngine.calculateIntegral('x', 'x', { lower: '0', upper: '1' })
      
      expect(result.alternativeFormats).toBeDefined()
      expect(result.alternativeFormats.some(format => format.includes('∫[0 to 1]'))).toBe(true)
    })
  })
})
