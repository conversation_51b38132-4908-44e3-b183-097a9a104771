import { describe, it, expect } from 'vitest'
import { calculusEngine } from '@/lib/calculus-engine'
import { mathEngine } from '@/lib/math-engine'
import { nlpProcessor } from '@/lib/nlp-processor'

describe('Limit Calculations', () => {
  describe('Basic Limits', () => {
    it('should calculate limit of x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
      expect(result.originalExpression).toBe('x')
    })

    it('should calculate limit of x as x approaches 2 correctly', async () => {
      const result = await calculusEngine.calculateLimit('x', { variable: 'x', approaches: '2' })
      
      expect(result.result).toBe('2')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of constant as x approaches any value', async () => {
      const result = await calculusEngine.calculateLimit('5', { variable: 'x', approaches: '10' })
      
      expect(result.result).toBe('5')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of x^2 as x approaches 3 correctly', async () => {
      const result = await calculusEngine.calculateLimit('x^2', { variable: 'x', approaches: '3' })
      
      expect(result.result).toBe('9')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of 2x + 1 as x approaches 4 correctly', async () => {
      const result = await calculusEngine.calculateLimit('2*x + 1', { variable: 'x', approaches: '4' })
      
      expect(result.result).toBe('9')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Standard Trigonometric Limits', () => {
    it('should calculate limit of sin(x)/x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('sin(x)/x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('1')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of (1-cos(x))/x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(1-cos(x))/x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of tan(x)/x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('tan(x)/x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('1')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Indeterminate Forms', () => {
    it('should calculate limit of (x^2-1)/(x-1) as x approaches 1 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(x^2-1)/(x-1)', { variable: 'x', approaches: '1' })
      
      expect(result.result).toBe('2')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of (x^2-4)/(x-2) as x approaches 2 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(x^2-4)/(x-2)', { variable: 'x', approaches: '2' })
      
      expect(result.result).toBe('4')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of (x^3-8)/(x-2) as x approaches 2 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(x^3-8)/(x-2)', { variable: 'x', approaches: '2' })
      
      expect(result.result).toBe('12')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of (x^2+x-2)/(x-1) as x approaches 1 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(x^2+x-2)/(x-1)', { variable: 'x', approaches: '1' })
      
      expect(result.result).toBe('3')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Limits at Infinity', () => {
    it('should calculate limit of 1/x as x approaches infinity correctly', async () => {
      const result = await calculusEngine.calculateLimit('1/x', { variable: 'x', approaches: 'infinity' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of x as x approaches infinity correctly', async () => {
      const result = await calculusEngine.calculateLimit('x', { variable: 'x', approaches: 'infinity' })
      
      expect(result.result).toBe('infinity')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of 1/x^2 as x approaches infinity correctly', async () => {
      const result = await calculusEngine.calculateLimit('1/x^2', { variable: 'x', approaches: 'infinity' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Exponential and Logarithmic Limits', () => {
    it('should calculate limit of e^x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('e^x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('1')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of ln(x) as x approaches 1 correctly', async () => {
      const result = await calculusEngine.calculateLimit('ln(x)', { variable: 'x', approaches: '1' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate limit of (e^x - 1)/x as x approaches 0 correctly', async () => {
      const result = await calculusEngine.calculateLimit('(e^x - 1)/x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('1')
      expect(result.operation).toBe('limit')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Natural Language Input Processing', () => {
    it('should process "limit of x as x approaches 0" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('limit of x as x approaches 0')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('x')
      expect(intent.variables).toEqual(['x'])
      expect(intent.approaches).toBe('0')
      expect(result.formattedValue).toBe('0')
    })

    it('should process "limit of sin(x)/x as x approaches 0" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('limit of sin(x)/x as x approaches 0')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('sin(x)/x')
      expect(intent.variables).toEqual(['x'])
      expect(intent.approaches).toBe('0')
      expect(result.formattedValue).toBe('1')
    })

    it('should process "what is the limit as x goes to 1 of (x^2-1)/(x-1)" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('what is the limit as x goes to 1 of (x^2-1)/(x-1)')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('(x^2-1)/(x-1)')
      expect(intent.variables).toEqual(['x'])
      expect(intent.approaches).toBe('1')
      expect(result.formattedValue).toBe('2')
    })

    it('should process "limit of x^2 when x approaches 3" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('limit of x^2 when x approaches 3')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('x^2')
      expect(intent.variables).toEqual(['x'])
      expect(intent.approaches).toBe('3')
      expect(result.formattedValue).toBe('9')
    })

    it('should process "lim[x→0] sin(x)/x" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('lim[x→0] sin(x)/x')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('sin(x)/x')
      expect(intent.variables).toEqual(['x'])
      expect(intent.approaches).toBe('0')
      expect(result.formattedValue).toBe('1')
    })
  })

  describe('Step-by-Step Solutions', () => {
    it('should provide step-by-step solution for basic limit', async () => {
      const result = await calculusEngine.calculateLimit('x^2', { variable: 'x', approaches: '3' })
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('limit')
      expect(result.steps.some(step => step.rule === 'Limit Evaluation')).toBe(true)
    })

    it('should provide step-by-step solution for indeterminate form', async () => {
      const result = await calculusEngine.calculateLimit('(x^2-1)/(x-1)', { variable: 'x', approaches: '1' })
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('limit')
      expect(result.steps.some(step => step.rule === 'Indeterminate Form Check')).toBe(true)
    })

    it('should provide step-by-step solution for standard trigonometric limit', async () => {
      const result = await calculusEngine.calculateLimit('sin(x)/x', { variable: 'x', approaches: '0' })
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('limit')
      expect(result.steps.some(step => step.rule === 'Standard Trigonometric Limit')).toBe(true)
    })
  })

  describe('Alternative Formats', () => {
    it('should provide alternative formats for limit results', async () => {
      const result = await calculusEngine.calculateLimit('x^2', { variable: 'x', approaches: '3' })
      
      expect(result.alternativeFormats).toBeDefined()
      expect(result.alternativeFormats).toContain('9')
      expect(result.alternativeFormats).toContain('lim[x→3] x^2')
      expect(result.alternativeFormats).toContain('L = 9')
    })

    it('should provide alternative formats for standard limit results', async () => {
      const result = await calculusEngine.calculateLimit('sin(x)/x', { variable: 'x', approaches: '0' })
      
      expect(result.alternativeFormats).toBeDefined()
      expect(result.alternativeFormats).toContain('1')
      expect(result.alternativeFormats).toContain('lim[x→0] sin(x)/x')
      expect(result.alternativeFormats).toContain('L = 1')
    })
  })
})
