import { describe, it, expect } from 'vitest'
import { nlpProcessor } from '@/lib/nlp-processor'
import { mathEngine } from '@/lib/math-engine'

describe('NLP Integration for Calculus', () => {
  describe('Input Type Detection', () => {
    it('should correctly detect calculus expressions as natural language', () => {
      const calculusInputs = [
        'derivative of sin(x)',
        'integral of x^2',
        'limit of x as x approaches 0',
        'differentiate cos(x)',
        'integrate e^x',
        'antiderivative of tan(x)',
      ]

      for (const input of calculusInputs) {
        const inputType = nlpProcessor.detectInputType(input)
        expect(['natural', 'mixed']).toContain(inputType)
      }
    })

    it('should distinguish between pure mathematical and calculus expressions', () => {
      const pureExpressions = [
        'sin(x)',
        'x^2 + 3x + 1',
        '2 + 3*4',
        'sqrt(16)',
      ]

      const calculusExpressions = [
        'derivative of sin(x)',
        'integral of x^2',
        'd/dx(cos(x))',
      ]

      for (const expr of pureExpressions) {
        const inputType = nlpProcessor.detectInputType(expr)
        expect(inputType).toBe('mathematical')
      }

      for (const expr of calculusExpressions) {
        const inputType = nlpProcessor.detectInputType(expr)
        expect(['natural', 'mixed']).toContain(inputType)
      }
    })
  })

  describe('Derivative Pattern Matching', () => {
    it('should parse various derivative phrasings correctly', () => {
      const derivativeTests = [
        { input: 'derivative of sin(x)', expression: 'sin(x)', variable: 'x' },
        { input: 'differentiate x^2', expression: 'x^2', variable: 'x' },
        { input: 'd/dx(cos(x))', expression: 'cos(x)', variable: 'x' },
        { input: 'd/dy(y^3)', expression: 'y^3', variable: 'y' },
        { input: 'what is the derivative of e^x', expression: 'e^x', variable: 'x' },
        { input: 'derive tan(x)', expression: 'tan(x)', variable: 'x' },
        { input: 'find the derivative of ln(x)', expression: 'ln(x)', variable: 'x' },
      ]

      for (const test of derivativeTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('differentiate')
        expect(intent.expression).toBe(test.expression)
        expect(intent.variables).toEqual([test.variable])
      }
    })

    it('should handle derivative expressions with respect to different variables', () => {
      const variableTests = [
        { input: 'derivative of x^2 with respect to x', variable: 'x' },
        { input: 'differentiate y^3 with respect to y', variable: 'y' },
        { input: 'derivative of t^2 with respect to t', variable: 't' },
        { input: 'd/dz(z^4)', variable: 'z' },
      ]

      for (const test of variableTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('differentiate')
        expect(intent.variables).toEqual([test.variable])
      }
    })

    it('should default to x when no variable is specified', () => {
      const inputs = [
        'derivative of sin(x)',
        'differentiate x^2',
        'derive cos(x)',
      ]

      for (const input of inputs) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(intent.operation).toBe('differentiate')
        expect(intent.variables).toEqual(['x'])
      }
    })
  })

  describe('Integration Pattern Matching', () => {
    it('should parse various integration phrasings correctly', () => {
      const integrationTests = [
        { input: 'integral of x^2', expression: 'x^2', variable: 'x' },
        { input: 'integrate sin(x)', expression: 'sin(x)', variable: 'x' },
        { input: 'antiderivative of cos(x)', expression: 'cos(x)', variable: 'x' },
        { input: 'what is the integral of e^x', expression: 'e^x', variable: 'x' },
        { input: 'find the antiderivative of ln(x)', expression: 'ln(x)', variable: 'x' },
      ]

      for (const test of integrationTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('integrate')
        expect(intent.expression).toBe(test.expression)
        expect(intent.variables).toEqual([test.variable])
      }
    })

    it('should parse definite integral patterns correctly', () => {
      const definiteTests = [
        { 
          input: 'integral of x^2 from 0 to 1', 
          expression: 'x^2', 
          lower: '0', 
          upper: '1' 
        },
        { 
          input: 'integrate sin(x) from 0 to π', 
          expression: 'sin(x)', 
          lower: '0', 
          upper: 'π' 
        },
        { 
          input: 'evaluate the integral of x from 1 to 3', 
          expression: 'x', 
          lower: '1', 
          upper: '3' 
        },
      ]

      for (const test of definiteTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('definiteIntegrate')
        expect(intent.expression).toBe(test.expression)
        expect(intent.bounds).toEqual({ lower: test.lower, upper: test.upper })
      }
    })

    it('should handle integration with different variables', () => {
      const variableTests = [
        { input: 'integral of y^2 with respect to y', variable: 'y' },
        { input: 'integrate t^3 with respect to t', variable: 't' },
        { input: 'antiderivative of z with respect to z', variable: 'z' },
      ]

      for (const test of variableTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('integrate')
        expect(intent.variables).toEqual([test.variable])
      }
    })
  })

  describe('Limit Pattern Matching', () => {
    it('should parse various limit phrasings correctly', () => {
      const limitTests = [
        { 
          input: 'limit of x as x approaches 0', 
          expression: 'x', 
          variable: 'x', 
          approaches: '0' 
        },
        { 
          input: 'limit of sin(x)/x as x approaches 0', 
          expression: 'sin(x)/x', 
          variable: 'x', 
          approaches: '0' 
        },
        { 
          input: 'what is the limit as x goes to 1 of x^2', 
          expression: 'x^2', 
          variable: 'x', 
          approaches: '1' 
        },
        { 
          input: 'limit of (x^2-1)/(x-1) when x approaches 1', 
          expression: '(x^2-1)/(x-1)', 
          variable: 'x', 
          approaches: '1' 
        },
        { 
          input: 'lim[x→0] sin(x)/x', 
          expression: 'sin(x)/x', 
          variable: 'x', 
          approaches: '0' 
        },
      ]

      for (const test of limitTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('limit')
        expect(intent.expression).toBe(test.expression)
        expect(intent.variables).toEqual([test.variable])
        expect(intent.approaches).toBe(test.approaches)
      }
    })

    it('should handle limits at infinity', () => {
      const infinityTests = [
        { input: 'limit of 1/x as x approaches infinity', approaches: 'infinity' },
        { input: 'limit of x as x goes to ∞', approaches: '∞' },
        { input: 'lim[x→∞] 1/x^2', approaches: '∞' },
      ]

      for (const test of infinityTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('limit')
        expect(intent.approaches).toBe(test.approaches)
      }
    })

    it('should handle different limit variables', () => {
      const variableTests = [
        { input: 'limit of y^2 as y approaches 2', variable: 'y' },
        { input: 'limit of t as t goes to 0', variable: 't' },
        { input: 'lim[z→1] z^3', variable: 'z' },
      ]

      for (const test of variableTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.operation).toBe('limit')
        expect(intent.variables).toEqual([test.variable])
      }
    })
  })

  describe('Complex Expression Parsing', () => {
    it('should handle expressions with multiple mathematical functions', () => {
      const complexTests = [
        'derivative of sin(x)*cos(x)',
        'integral of e^x*ln(x)',
        'limit of tan(x)/sin(x) as x approaches 0',
        'differentiate sqrt(x^2 + 1)',
        'integrate x*sin(x)*cos(x)',
      ]

      for (const input of complexTests) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(['differentiate', 'integrate', 'limit']).toContain(intent.operation)
        expect(intent.expression).toBeTruthy()
        expect(intent.expression.length).toBeGreaterThan(0)
      }
    })

    it('should handle expressions with parentheses and nested functions', () => {
      const nestedTests = [
        'derivative of sin(cos(x))',
        'integral of ln(sin(x))',
        'limit of (sin(x) - x)/x^3 as x approaches 0',
      ]

      for (const input of nestedTests) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(['differentiate', 'integrate', 'limit']).toContain(intent.operation)
        expect(intent.expression).toContain('(')
        expect(intent.expression).toContain(')')
      }
    })

    it('should preserve mathematical notation in expressions', () => {
      const notationTests = [
        { input: 'derivative of x^2', expected: 'x^2' },
        { input: 'integral of e^x', expected: 'e^x' },
        { input: 'limit of sin(π*x) as x approaches 0', expected: 'sin(π*x)' },
        { input: 'differentiate sqrt(x)', expected: 'sqrt(x)' },
      ]

      for (const test of notationTests) {
        const intent = nlpProcessor.parseNaturalLanguage(test.input)
        
        expect(intent.expression).toBe(test.expected)
      }
    })
  })

  describe('End-to-End NLP to Result Flow', () => {
    it('should process complete derivative flow from NLP to result', async () => {
      const input = 'derivative of sin(x)'
      
      const intent = nlpProcessor.parseNaturalLanguage(input)
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('sin(x)')
      expect(result.formattedValue).toBe('cos(x)')
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
    })

    it('should process complete integration flow from NLP to result', async () => {
      const input = 'integral of x^2'
      
      const intent = nlpProcessor.parseNaturalLanguage(input)
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('integrate')
      expect(intent.expression).toBe('x^2')
      expect(result.formattedValue).toContain('x^3')
      expect(result.formattedValue).toContain('+ C')
    })

    it('should process complete limit flow from NLP to result', async () => {
      const input = 'limit of x as x approaches 2'
      
      const intent = nlpProcessor.parseNaturalLanguage(input)
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('limit')
      expect(intent.expression).toBe('x')
      expect(intent.approaches).toBe('2')
      expect(result.formattedValue).toBe('2')
    })

    it('should process definite integral flow from NLP to result', async () => {
      const input = 'integral of x from 0 to 2'
      
      const intent = nlpProcessor.parseNaturalLanguage(input)
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('definiteIntegrate')
      expect(intent.expression).toBe('x')
      expect(intent.bounds).toEqual({ lower: '0', upper: '2' })
      expect(result.formattedValue).toBe('2')
    })
  })

  describe('Synonym and Alternative Phrasing Support', () => {
    it('should handle different words for derivative', () => {
      const synonyms = [
        'derivative of x^2',
        'differentiate x^2',
        'derive x^2',
        'find the derivative of x^2',
        'what is the derivative of x^2',
        'd/dx(x^2)',
      ]

      for (const input of synonyms) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(intent.operation).toBe('differentiate')
        expect(intent.expression).toBe('x^2')
      }
    })

    it('should handle different words for integral', () => {
      const synonyms = [
        'integral of x',
        'integrate x',
        'antiderivative of x',
        'find the integral of x',
        'what is the integral of x',
        'find the antiderivative of x',
      ]

      for (const input of synonyms) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(intent.operation).toBe('integrate')
        expect(intent.expression).toBe('x')
      }
    })

    it('should handle different limit phrasings', () => {
      const synonyms = [
        'limit of x as x approaches 0',
        'limit of x as x goes to 0',
        'limit of x when x approaches 0',
        'what is the limit of x as x approaches 0',
        'find the limit of x as x approaches 0',
      ]

      for (const input of synonyms) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(intent.operation).toBe('limit')
        expect(intent.expression).toBe('x')
        expect(intent.approaches).toBe('0')
      }
    })
  })

  describe('Case Sensitivity and Formatting', () => {
    it('should handle different case variations', () => {
      const caseVariations = [
        'Derivative of sin(x)',
        'INTEGRAL OF X^2',
        'Limit Of X As X Approaches 0',
        'DIFFERENTIATE COS(X)',
      ]

      for (const input of caseVariations) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(['differentiate', 'integrate', 'limit']).toContain(intent.operation)
        expect(intent.expression).toBeTruthy()
      }
    })

    it('should handle extra whitespace and formatting', () => {
      const formattingVariations = [
        '  derivative   of   sin(x)  ',
        'integral  of  x^2',
        'limit  of  x  as  x  approaches  0',
        '\tderivative\tof\tx^2\t',
      ]

      for (const input of formattingVariations) {
        const intent = nlpProcessor.parseNaturalLanguage(input)
        
        expect(['differentiate', 'integrate', 'limit']).toContain(intent.operation)
        expect(intent.expression).toBeTruthy()
      }
    })
  })
})
