import { describe, it, expect } from 'vitest'
import { calculusEngine } from '@/lib/calculus-engine'
import { mathEngine } from '@/lib/math-engine'
import { nlpProcessor } from '@/lib/nlp-processor'

describe('Symbolic Result Preservation', () => {
  describe('Critical Bug Prevention Tests', () => {
    it('should return "cos(x)" for derivative of sin(x), NOT "0" or "1"', async () => {
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      // This is the critical test that was failing before the fix
      expect(result.result).toBe('cos(x)')
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('1')
      expect(result.result).not.toBe(0)
      expect(result.result).not.toBe(1)
      
      // Ensure it's a string, not a number
      expect(typeof result.result).toBe('string')
      expect(result.result).toMatch(/cos\(x\)/)
    })

    it('should return "2 * x" for derivative of x^2, NOT "0" or "2"', async () => {
      const result = await calculusEngine.calculateDerivative('x^2', 'x')
      
      // This is the critical test that was failing before the fix
      expect(result.result).toBe('2 * x')
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('2')
      expect(result.result).not.toBe(0)
      expect(result.result).not.toBe(2)
      
      // Ensure it's a string, not a number
      expect(typeof result.result).toBe('string')
      expect(result.result).toMatch(/2.*x/)
    })

    it('should return "-sin(x)" for derivative of cos(x), NOT "0" or "-1"', async () => {
      const result = await calculusEngine.calculateDerivative('cos(x)', 'x')
      
      expect(result.result).toBe('-sin(x)')
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('-1')
      expect(result.result).not.toBe(0)
      expect(result.result).not.toBe(-1)
      
      // Ensure it's a string, not a number
      expect(typeof result.result).toBe('string')
      expect(result.result).toMatch(/-sin\(x\)/)
    })

    it('should return "3 * x^2" for derivative of x^3, NOT "0" or "3"', async () => {
      const result = await calculusEngine.calculateDerivative('x^3', 'x')
      
      expect(result.result).toBe('3 * x^2')
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('3')
      expect(result.result).not.toBe(0)
      expect(result.result).not.toBe(3)
      
      // Ensure it's a string, not a number
      expect(typeof result.result).toBe('string')
      expect(result.result).toMatch(/3.*x\^2/)
    })
  })

  describe('End-to-End Symbolic Preservation', () => {
    it('should preserve symbolic results through full NLP flow for derivatives', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('derivative of sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      // Test the complete flow from NLP to final result
      expect(result.value).toBe('cos(x)')
      expect(result.formattedValue).toBe('cos(x)')
      expect(typeof result.value).toBe('string')
      expect(typeof result.formattedValue).toBe('string')
      
      // Ensure no evaluation occurred
      expect(result.value).not.toBe('0')
      expect(result.value).not.toBe('1')
      expect(result.formattedValue).not.toBe('0')
      expect(result.formattedValue).not.toBe('1')
    })

    it('should preserve symbolic results through full NLP flow for integrals', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('integral of x^2')
      const result = await mathEngine.processIntent(intent)
      
      // Test the complete flow from NLP to final result
      expect(result.formattedValue).toContain('x^3')
      expect(result.formattedValue).toContain('+ C')
      expect(typeof result.value).toBe('string')
      expect(typeof result.formattedValue).toBe('string')
      
      // Ensure no evaluation occurred
      expect(result.formattedValue).not.toBe('0')
      expect(result.formattedValue).not.toBe('1')
    })

    it('should preserve symbolic results for complex expressions', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('derivative of x*sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      // Test product rule result preservation
      expect(result.formattedValue).toBe('sin(x) + x * cos(x)')
      expect(typeof result.value).toBe('string')
      expect(typeof result.formattedValue).toBe('string')
      
      // Ensure no evaluation occurred
      expect(result.formattedValue).not.toBe('0')
      expect(result.formattedValue).not.toBe('1')
    })
  })

  describe('Variable Independence Tests', () => {
    it('should not evaluate symbolic expressions at x=0', async () => {
      const tests = [
        { expr: 'sin(x)', expected: 'cos(x)', notExpected: ['1', '0'] },
        { expr: 'cos(x)', expected: '-sin(x)', notExpected: ['0', '-1'] },
        { expr: 'x^2', expected: '2 * x', notExpected: ['0', '2'] },
        { expr: 'x^3', expected: '3 * x^2', notExpected: ['0', '3'] },
        { expr: 'e^x', expected: 'e^x', notExpected: ['1'] },
      ]

      for (const test of tests) {
        const result = await calculusEngine.calculateDerivative(test.expr, 'x')
        
        expect(result.result).toBe(test.expected)
        for (const notExpected of test.notExpected) {
          expect(result.result).not.toBe(notExpected)
        }
        expect(typeof result.result).toBe('string')
      }
    })

    it('should not evaluate symbolic expressions at any specific x value', async () => {
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      // cos(x) should not be evaluated at common test points
      expect(result.result).not.toBe('1')     // cos(0)
      expect(result.result).not.toBe('0')     // cos(π/2)
      expect(result.result).not.toBe('-1')    // cos(π)
      expect(result.result).not.toBe('0.5')   // cos(π/3)
      
      // Should remain symbolic
      expect(result.result).toBe('cos(x)')
    })

    it('should preserve variables in polynomial derivatives', async () => {
      const result = await calculusEngine.calculateDerivative('x^4 + 3*x^2 + 5', 'x')
      
      expect(result.result).toBe('4 * x^3 + 6 * x')
      expect(result.result).toContain('x')
      expect(typeof result.result).toBe('string')
      
      // Should not be evaluated at any point
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('10')  // 4*1^3 + 6*1 at x=1
      expect(result.result).not.toBe('40')  // 4*2^3 + 6*2 at x=2
    })
  })

  describe('Type Safety Tests', () => {
    it('should always return string results for symbolic operations', async () => {
      const expressions = ['sin(x)', 'cos(x)', 'x^2', 'x^3', 'e^x', 'ln(x)', 'tan(x)']
      
      for (const expr of expressions) {
        const result = await calculusEngine.calculateDerivative(expr, 'x')
        
        expect(typeof result.result).toBe('string')
        expect(result.result).not.toBeInstanceOf(Number)
        expect(isNaN(Number(result.result))).toBe(true) // Should not be a pure number
      }
    })

    it('should maintain string type through math engine processing', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('derivative of sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      expect(typeof result.value).toBe('string')
      expect(typeof result.formattedValue).toBe('string')
      expect(result.value).not.toBeInstanceOf(Number)
      expect(result.formattedValue).not.toBeInstanceOf(Number)
    })

    it('should not convert symbolic results to numbers', async () => {
      const result = await calculusEngine.calculateDerivative('x^2', 'x')
      
      // Ensure the result is not accidentally converted to a number
      expect(result.result).toBe('2 * x')
      expect(Number.isNaN(Number(result.result))).toBe(true)
      expect(parseFloat(result.result)).toBeNaN()
      expect(parseInt(result.result)).toBeNaN()
    })
  })

  describe('Integration Symbolic Preservation', () => {
    it('should preserve symbolic results for integrals', async () => {
      const tests = [
        { expr: 'x', expected: /x\^2.*\+ C/, notExpected: ['0.5', '1'] },
        { expr: 'x^2', expected: /x\^3.*\+ C/, notExpected: ['0.33', '1'] },
        { expr: 'sin(x)', expected: /-cos\(x\).*\+ C/, notExpected: ['0', '1'] },
        { expr: 'cos(x)', expected: /sin\(x\).*\+ C/, notExpected: ['0', '1'] },
      ]

      for (const test of tests) {
        const result = await calculusEngine.calculateIntegral(test.expr, 'x')
        
        expect(result.result).toMatch(test.expected)
        for (const notExpected of test.notExpected) {
          expect(result.result).not.toBe(notExpected)
        }
        expect(typeof result.result).toBe('string')
      }
    })

    it('should preserve symbolic results for definite integrals', async () => {
      const result = await calculusEngine.calculateIntegral('x', 'x', { lower: '0', upper: '2' })
      
      // Definite integrals should evaluate to numbers, but correctly
      expect(result.result).toBe('2')
      expect(typeof result.result).toBe('string')
      expect(result.result).not.toBe('0')
      expect(result.result).not.toBe('1')
    })
  })

  describe('Limit Symbolic Preservation', () => {
    it('should preserve symbolic results when appropriate for limits', async () => {
      // Test limits that should remain symbolic
      const result = await calculusEngine.calculateLimit('x', { variable: 'x', approaches: 'a' })
      
      expect(result.result).toBe('a')
      expect(typeof result.result).toBe('string')
    })

    it('should evaluate limits correctly without premature evaluation', async () => {
      const result = await calculusEngine.calculateLimit('sin(x)/x', { variable: 'x', approaches: '0' })
      
      expect(result.result).toBe('1')
      expect(typeof result.result).toBe('string')
      expect(result.result).not.toBe('0')
    })
  })

  describe('Regression Prevention', () => {
    it('should never return 0 for derivative of sin(x)', async () => {
      // This test specifically prevents the regression we fixed
      for (let i = 0; i < 10; i++) {
        const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
        expect(result.result).toBe('cos(x)')
        expect(result.result).not.toBe('0')
        expect(result.result).not.toBe(0)
      }
    })

    it('should never return 1 for derivative of x^2', async () => {
      // This test specifically prevents the regression we fixed
      for (let i = 0; i < 10; i++) {
        const result = await calculusEngine.calculateDerivative('x^2', 'x')
        expect(result.result).toBe('2 * x')
        expect(result.result).not.toBe('1')
        expect(result.result).not.toBe(1)
      }
    })

    it('should maintain consistency across multiple calls', async () => {
      const expressions = ['sin(x)', 'cos(x)', 'x^2', 'x^3']
      const expectedResults = ['cos(x)', '-sin(x)', '2 * x', '3 * x^2']
      
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < expressions.length; j++) {
          const result = await calculusEngine.calculateDerivative(expressions[j], 'x')
          expect(result.result).toBe(expectedResults[j])
        }
      }
    })
  })
})
