@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.205 0 0); /* #1f2937 */
  --card: oklch(0.97 0 0); /* #f1f5f9 */
  --card-foreground: oklch(0.205 0 0); /* #1f2937 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.205 0 0); /* #1f2937 */
  --primary: oklch(0.205 0 0); /* #1f2937 */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.556 0 0); /* #6b7280 */
  --secondary-foreground: oklch(1 0 0); /* #ffffff */
  --muted: oklch(0.97 0 0); /* #f1f5f9 */
  --muted-foreground: oklch(0.322 0 0); /* #374151 */
  --accent: oklch(0.646 0.222 264.376); /* #8b5cf6 */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.577 0.245 27.325); /* #dc2626 */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.922 0 0); /* #e5e7eb */
  --input: oklch(0.97 0 0); /* #f1f5f9 */
  --ring: oklch(0.646 0.222 264.376); /* #8b5cf6 */
  --chart-1: oklch(0.646 0.222 264.376); /* #8b5cf6 */
  --chart-2: oklch(0.577 0.245 27.325); /* #dc2626 */
  --chart-3: oklch(0.398 0.07 227.392); /* #a16207 */
  --chart-4: oklch(0.205 0 0); /* #1f2937 */
  --chart-5: oklch(0.769 0.188 70.08); /* #f59e0b */
  --radius: 0.5rem;
  --sidebar: oklch(0.97 0 0); /* #f1f5f9 */
  --sidebar-foreground: oklch(0.205 0 0); /* #1f2937 */
  --sidebar-primary: oklch(0.205 0 0); /* #1f2937 */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.646 0.222 264.376); /* #8b5cf6 */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.922 0 0); /* #e5e7eb */
  --sidebar-ring: oklch(0.646 0.222 264.376); /* #8b5cf6 */
}

.dark {
  --background: oklch(0.145 0 0); /* Dark background */
  --foreground: oklch(0.985 0 0); /* Light text */
  --card: oklch(0.205 0 0); /* Darker card background */
  --card-foreground: oklch(0.985 0 0); /* Light card text */
  --popover: oklch(0.145 0 0); /* Dark popover */
  --popover-foreground: oklch(0.985 0 0); /* Light popover text */
  --primary: oklch(0.646 0.222 264.376); /* Purple primary in dark mode */
  --primary-foreground: oklch(1 0 0); /* White text on primary */
  --secondary: oklch(0.322 0 0); /* Darker secondary */
  --secondary-foreground: oklch(0.985 0 0); /* Light secondary text */
  --muted: oklch(0.269 0 0); /* Muted dark background */
  --muted-foreground: oklch(0.708 0 0); /* Muted light text */
  --accent: oklch(0.646 0.222 264.376); /* Purple accent */
  --accent-foreground: oklch(1 0 0); /* White accent text */
  --destructive: oklch(0.577 0.245 27.325); /* Red destructive */
  --destructive-foreground: oklch(1 0 0); /* White destructive text */
  --border: oklch(0.322 0 0); /* Dark borders */
  --input: oklch(0.269 0 0); /* Dark input background */
  --ring: oklch(0.646 0.222 264.376); /* Purple focus ring */
  --chart-1: oklch(0.646 0.222 264.376);
  --chart-2: oklch(0.577 0.245 27.325);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.985 0 0);
  --chart-5: oklch(0.828 0.189 84.429);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.646 0.222 264.376);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.322 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.322 0 0);
  --sidebar-ring: oklch(0.646 0.222 264.376);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
