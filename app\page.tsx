"use client"

import { useState, useEffect } from "react"
import { <PERSON>culator } from "@/components/calculator"
import { CalculationHistory } from "@/components/calculation-history"
import { HistorySearch } from "@/components/history-search"
import { UserGuide } from "@/components/user-guide"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { History, CalculatorIcon, BookOpen } from "lucide-react"
import { historyManager } from "@/lib/history-manager"
import type { Calculation } from "@/lib/types"

export default function HomePage() {
  const [calculations, setCalculations] = useState<Calculation[]>([])
  const [showHistory, setShowHistory] = useState(false)
  const [showGuide, setShowGuide] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [calculatorInput, setCalculatorInput] = useState("")

  useEffect(() => {
    const initializeHistory = async () => {
      try {
        await historyManager.init()
        const history = await historyManager.getHistory()
        setCalculations(history)
      } catch (error) {
        console.error("Failed to initialize history:", error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeHistory()
  }, [])

  const handleNewCalculation = async (calculation: Calculation) => {
    try {
      await historyManager.saveCalculation(calculation)
      setCalculations((prev) => [calculation, ...prev])
    } catch (error) {
      console.error("Failed to save calculation:", error)
      // Still add to local state even if save fails
      setCalculations((prev) => [calculation, ...prev])
    }
  }

  const handleSearchResults = (results: Calculation[]) => {
    setCalculations(results)
  }

  const handleSelectCalculation = (calculation: Calculation) => {
    // This could be used to populate the calculator input with the selected calculation
    setCalculatorInput(calculation.input)
    console.log("Selected calculation:", calculation)
  }

  const handleGuideExampleClick = (example: string) => {
    setCalculatorInput(example)
    setShowGuide(false) // Close guide and return to calculator
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-accent border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading calculator...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-accent text-accent-foreground">
                <CalculatorIcon className="w-5 h-5" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">AI Calculator</h1>
                <p className="text-sm text-muted-foreground">Natural language math solver</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowGuide(!showGuide)}
                className="flex items-center gap-2"
              >
                <BookOpen className="w-4 h-4" />
                Guide
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistory(!showHistory)}
                className="flex items-center gap-2"
              >
                <History className="w-4 h-4" />
                History
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {showGuide ? (
            /* User Guide */
            <UserGuide onExampleClick={handleGuideExampleClick} />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Calculator */}
              <div className="lg:col-span-2">
                <Card className="p-6">
                  <Calculator
                    onCalculation={handleNewCalculation}
                    initialInput={calculatorInput}
                    onInputChange={setCalculatorInput}
                  />
                </Card>
              </div>

            {/* History Sidebar */}
            <div className={`${showHistory ? "block" : "hidden lg:block"}`}>
              <Card className="p-4">
                <Tabs defaultValue="recent" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="recent">Recent</TabsTrigger>
                    <TabsTrigger value="search">Search</TabsTrigger>
                  </TabsList>

                  <TabsContent value="recent" className="mt-4">
                    <h2 className="text-lg font-semibold mb-4 text-foreground">Recent Calculations</h2>
                    <CalculationHistory
                      calculations={calculations.slice(0, 10)}
                      onSelectCalculation={handleSelectCalculation}
                    />
                  </TabsContent>

                  <TabsContent value="search" className="mt-4">
                    <h2 className="text-lg font-semibold mb-4 text-foreground">Search History</h2>
                    <HistorySearch onResultsChange={handleSearchResults} />
                    <div className="mt-4">
                      <CalculationHistory
                        calculations={calculations.slice(0, 20)}
                        onSelectCalculation={handleSelectCalculation}
                      />
                    </div>
                  </TabsContent>
                </Tabs>
              </Card>
            </div>
          </div>
          )}

          {/* Features Section - Only show when not in guide mode */}
          {!showGuide && (
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6 text-center">
              <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-accent/10 flex items-center justify-center">
                <span className="text-2xl">🗣️</span>
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Natural Language</h3>
              <p className="text-sm text-muted-foreground">
                Ask questions like "What is the square root of 144?" or "Solve x² + 5x + 6 = 0"
              </p>
            </Card>

            <Card className="p-6 text-center">
              <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-accent/10 flex items-center justify-center">
                <span className="text-2xl">📝</span>
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Step-by-Step</h3>
              <p className="text-sm text-muted-foreground">
                Get detailed explanations showing how each problem is solved
              </p>
            </Card>

            <Card className="p-6 text-center">
              <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-accent/10 flex items-center justify-center">
                <span className="text-2xl">🧮</span>
              </div>
              <h3 className="font-semibold mb-2 text-foreground">Advanced Math</h3>
              <p className="text-sm text-muted-foreground">Support for calculus, algebra, trigonometry, and more</p>
            </Card>
          </div>
          )}
        </div>
      </main>
    </div>
  )
}
