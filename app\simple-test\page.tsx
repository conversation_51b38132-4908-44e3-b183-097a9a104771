"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function SimpleTestPage() {
  const [result, setResult] = useState<string>("")

  const testMathJS = async () => {
    try {
      const { derivative, simplify } = await import('mathjs')
      
      // Test derivative
      const derivResult = derivative("sin(x)", "x")
      const simplified = simplify(derivResult)
      
      setResult(`
Math.js Test Results:
- derivative("sin(x)", "x") = ${derivResult.toString()}
- simplified = ${simplified.toString()}
- type = ${typeof derivResult}
- constructor = ${derivResult.constructor.name}
- evaluated at x=0 = ${derivResult.evaluate({x: 0})}

Test 2: x^2
- derivative("x^2", "x") = ${derivative("x^2", "x").toString()}
- evaluated at x=0 = ${derivative("x^2", "x").evaluate({x: 0})}
      `)
    } catch (error) {
      setResult(`Error: ${(error as Error).message}`)
    }
  }

  const testCalculusEngine = async () => {
    try {
      const { calculusEngine } = await import('@/lib/calculus-engine')
      
      const result1 = await calculusEngine.calculateDerivative("sin(x)", "x")
      const result2 = await calculusEngine.calculateDerivative("x^2", "x")
      
      setResult(`
Calculus Engine Test Results:
- sin(x) derivative = ${result1.result}
- x^2 derivative = ${result2.result}
- sin(x) type = ${typeof result1.result}
- x^2 type = ${typeof result2.result}
      `)
    } catch (error) {
      setResult(`Calculus Engine Error: ${(error as Error).message}`)
    }
  }

  const testNLPFlow = async () => {
    try {
      const { nlpProcessor } = await import('@/lib/nlp-processor')
      const { mathEngine } = await import('@/lib/math-engine')
      
      // Test the full flow
      const intent = nlpProcessor.parseNaturalLanguage("derivative of sin(x)")
      const result = await mathEngine.processIntent(intent)
      
      setResult(`
NLP Flow Test Results:
- Input: "derivative of sin(x)"
- Intent: ${JSON.stringify(intent)}
- Result value: ${result.value}
- Result formattedValue: ${result.formattedValue}
- Result type: ${typeof result.value}
      `)
    } catch (error) {
      setResult(`NLP Flow Error: ${(error as Error).message}`)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Simple Calculus Test</h1>
      
      <div className="flex gap-4">
        <Button onClick={testMathJS}>Test Math.js</Button>
        <Button onClick={testCalculusEngine}>Test Calculus Engine</Button>
        <Button onClick={testNLPFlow}>Test NLP Flow</Button>
      </div>

      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Results</h2>
        <pre className="text-sm whitespace-pre-wrap">{result}</pre>
      </Card>
    </div>
  )
}
