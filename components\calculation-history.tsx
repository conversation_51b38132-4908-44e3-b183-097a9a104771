"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, Copy, RotateCcw } from "lucide-react"
import type { Calculation } from "@/lib/types"

interface CalculationHistoryProps {
  calculations: Calculation[]
  onSelectCalculation: (calculation: Calculation) => void
}

export function CalculationHistory({ calculations, onSelectCalculation }: CalculationHistoryProps) {
  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).format(date)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  if (calculations.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="w-8 h-8 mx-auto mb-3 text-muted-foreground" />
        <p className="text-sm text-muted-foreground">No calculations yet</p>
        <p className="text-xs text-muted-foreground mt-1">Your calculation history will appear here</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {calculations.map((calculation) => (
        <Card key={calculation.id} className="p-3 hover:bg-accent/5 transition-colors">
          <div className="space-y-2">
            {/* Input */}
            <div className="flex items-start justify-between gap-2">
              <p className="text-sm text-foreground line-clamp-2 flex-1">{calculation.input}</p>
              <Badge variant="outline" className="text-xs shrink-0">
                {calculation.inputType === "natural" ? "🗣️" : calculation.inputType === "mixed" ? "🔀" : "🧮"}
              </Badge>
            </div>

            {/* Result */}
            <div className="flex items-center justify-between">
              <p className="font-mono font-semibold text-accent">{calculation.result.formattedValue}</p>
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => copyToClipboard(calculation.result.formattedValue)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => onSelectCalculation(calculation)}
                >
                  <RotateCcw className="w-3 h-3" />
                </Button>
              </div>
            </div>

            {/* Timestamp */}
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">{formatTime(calculation.timestamp)}</span>
            </div>
          </div>
        </Card>
      ))}
    </div>
  )
}
