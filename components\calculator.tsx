"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, Send, Trash2 } from "lucide-react"
import { mathEngine } from "@/lib/math-engine"
import { nlpProcessor } from "@/lib/nlp-processor"
import type { Calculation, CalculationResult, Step } from "@/lib/types"

interface CalculatorProps {
  onCalculation: (calculation: Calculation) => void
  initialInput?: string
  onInputChange?: (input: string) => void
}

export function Calculator({ onCalculation, initialInput = "", onInputChange }: CalculatorProps) {
  const [input, setInput] = useState(initialInput)
  const [result, setResult] = useState<CalculationResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [inputType, setInputType] = useState<"natural" | "mathematical" | "mixed">("natural")

  // Update input when initialInput prop changes
  useEffect(() => {
    if (initialInput !== input) {
      setInput(initialInput)
      if (initialInput.trim()) {
        const detectedType = nlpProcessor.detectInputType(initialInput)
        setInputType(detectedType)
      }
    }
  }, [initialInput, input])

  const handleInputChange = (value: string) => {
    setInput(value)
    setError(null)

    // Call parent's input change handler if provided
    if (onInputChange) {
      onInputChange(value)
    }

    // Detect input type
    if (value.trim()) {
      const detectedType = nlpProcessor.detectInputType(value)
      setInputType(detectedType)
    }
  }

  const handleCalculate = async () => {
    if (!input.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      let expression = input.trim()

      let calculationResult: CalculationResult
      let steps: Step[] = []

      // Handle different input types
      if (inputType === "natural" || inputType === "mixed") {
        try {
          const intent = nlpProcessor.parseNaturalLanguage(input)

          // Check if it's a calculus operation
          if (["differentiate", "integrate", "definiteIntegrate", "limit"].includes(intent.operation)) {
            calculationResult = await mathEngine.processIntent(intent)
            steps = calculationResult.steps || []
          } else {
            // For other operations, use the expression
            expression = intent.expression
            const validation = mathEngine.validateExpression(expression)
            if (!validation.isValid) {
              throw new Error(validation.errors[0])
            }
            calculationResult = mathEngine.evaluate(expression)
            steps = mathEngine.generateSteps(expression, "evaluate")
          }
        } catch (nlpError) {
          // If NLP fails, try to use the input as-is
          console.warn("NLP parsing failed, using input as-is:", nlpError)
          const validation = mathEngine.validateExpression(expression)
          if (!validation.isValid) {
            throw new Error(validation.errors[0])
          }
          calculationResult = mathEngine.evaluate(expression)
          steps = mathEngine.generateSteps(expression, "evaluate")
        }
      } else {
        // Direct mathematical evaluation
        const validation = mathEngine.validateExpression(expression)
        if (!validation.isValid) {
          throw new Error(validation.errors[0])
        }
        calculationResult = mathEngine.evaluate(expression)
        steps = mathEngine.generateSteps(expression, "evaluate")
      }

      setResult(calculationResult)

      // Create calculation record
      const calculation: Calculation = {
        id: Date.now().toString(),
        input: input,
        inputType: inputType,
        result: calculationResult,
        steps: steps,
        timestamp: new Date(),
        tags: [],
      }

      onCalculation(calculation)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleClear = () => {
    setInput("")
    setResult(null)
    setError(null)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      handleCalculate()
    }
  }

  return (
    <div className="space-y-6">
      {/* Input Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-foreground">Enter your calculation</h2>
          <Badge variant="secondary" className="text-xs">
            {inputType === "natural" && "🗣️ Natural Language"}
            {inputType === "mathematical" && "🧮 Mathematical"}
            {inputType === "mixed" && "🔀 Mixed"}
          </Badge>
        </div>

        <div className="relative">
          <Textarea
            value={input}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Try: 'What is the square root of 144?' or '2 + 3 * 4'"
            className="min-h-[100px] text-base resize-none pr-12"
            disabled={isLoading}
          />
          <div className="absolute bottom-3 right-3 flex gap-2">
            {input && (
              <Button size="sm" variant="ghost" onClick={handleClear} disabled={isLoading}>
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
            <Button size="sm" onClick={handleCalculate} disabled={!input.trim() || isLoading}>
              {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        <p className="text-xs text-muted-foreground">
          Press Ctrl+Enter to calculate • Supports natural language and mathematical notation
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="p-4 border-destructive/20 bg-destructive/5">
          <p className="text-sm text-destructive">{error}</p>
        </Card>
      )}

      {/* Result Display */}
      {result && (
        <Card className="p-6 bg-accent/5 border-accent/20">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-foreground">Result</h3>
              <Badge variant={result.isExact ? "default" : "secondary"}>
                {result.isExact ? "Exact" : "Approximate"}
              </Badge>
            </div>

            <div className="text-3xl font-mono font-bold text-accent">
              {result.formattedValue}
              {result.unit && <span className="text-lg ml-2">{result.unit}</span>}
            </div>

            {result.alternativeFormats && result.alternativeFormats.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Alternative formats:</p>
                <div className="flex flex-wrap gap-2">
                  {result.alternativeFormats.map((format, index) => (
                    <Badge key={index} variant="outline" className="font-mono">
                      {format}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Example Queries */}
      <Card className="p-4 bg-muted/30">
        <h4 className="text-sm font-medium mb-3 text-foreground">Try these examples:</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {["What is 15% of 240?", "Solve x^2 - 5x + 6 = 0", "Square root of 169", "2^10", "sin(π/2)", "log(100)"].map(
            (example, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="justify-start text-left h-auto p-2 text-xs"
                onClick={() => handleInputChange(example)}
                disabled={isLoading}
              >
                {example}
              </Button>
            ),
          )}
        </div>
      </Card>
    </div>
  )
}
