"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Download, Trash2, Calendar } from "lucide-react"
import { historyManager } from "@/lib/history-manager"
import type { Calculation, HistoryFilter } from "@/lib/types"

interface HistorySearchProps {
  onResultsChange: (calculations: Calculation[]) => void
}

export function HistorySearch({ onResultsChange }: HistorySearchProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)
  const [filter, setFilter] = useState<HistoryFilter>({})
  const [isSearching, setIsSearching] = useState(false)

  const handleSearch = async () => {
    setIsSearching(true)
    try {
      let results: Calculation[]

      if (searchQuery.trim()) {
        results = await historyManager.searchHistory(searchQuery)
      } else {
        results = await historyManager.getHistory(filter)
      }

      onResultsChange(results)
    } catch (error) {
      console.error("Search failed:", error)
    } finally {
      setIsSearching(false)
    }
  }

  const handleFilterChange = (key: keyof HistoryFilter, value: any) => {
    const newFilter = { ...filter, [key]: value }
    setFilter(newFilter)
  }

  const handleExportHistory = async () => {
    try {
      const exportData = await historyManager.exportHistory()
      const blob = new Blob([exportData], { type: "application/json" })
      const url = URL.createObjectURL(blob)

      const a = document.createElement("a")
      a.href = url
      a.download = `calculator-history-${new Date().toISOString().split("T")[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Export failed:", error)
    }
  }

  const handleClearHistory = async () => {
    if (confirm("Are you sure you want to clear all calculation history? This cannot be undone.")) {
      try {
        await historyManager.clearHistory()
        onResultsChange([])
      } catch (error) {
        console.error("Clear history failed:", error)
      }
    }
  }

  useEffect(() => {
    handleSearch()
  }, [filter])

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search calculations..."
            className="pl-10"
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
          />
        </div>
        <Button onClick={handleSearch} disabled={isSearching}>
          {isSearching ? "Searching..." : "Search"}
        </Button>
        <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
          <Filter className="w-4 h-4" />
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card className="p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Input Type Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Input Type</label>
              <Select
                value={filter.inputType || "all"}
                onValueChange={(value) => handleFilterChange("inputType", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  <SelectItem value="natural">Natural Language</SelectItem>
                  <SelectItem value="mathematical">Mathematical</SelectItem>
                  <SelectItem value="mixed">Mixed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Complexity Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Complexity</label>
              <Select
                value={filter.complexity || "all"}
                onValueChange={(value) => handleFilterChange("complexity", value === "all" ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All levels</SelectItem>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div>
              <label className="text-sm font-medium mb-2 block">Date Range</label>
              <Select
                onValueChange={(value) => {
                  const now = new Date()
                  let start: Date | undefined

                  switch (value) {
                    case "today":
                      start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
                      break
                    case "week":
                      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
                      break
                    case "month":
                      start = new Date(now.getFullYear(), now.getMonth(), 1)
                      break
                    default:
                      start = undefined
                  }

                  handleFilterChange("dateRange", start ? { start, end: now } : undefined)
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">Last 7 days</SelectItem>
                  <SelectItem value="month">This month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters */}
          <div className="flex flex-wrap gap-2">
            {filter.inputType && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Type: {filter.inputType}
                <button
                  onClick={() => handleFilterChange("inputType", undefined)}
                  className="ml-1 hover:bg-background/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {filter.complexity && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Complexity: {filter.complexity}
                <button
                  onClick={() => handleFilterChange("complexity", undefined)}
                  className="ml-1 hover:bg-background/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
            {filter.dateRange && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                Date filtered
                <button
                  onClick={() => handleFilterChange("dateRange", undefined)}
                  className="ml-1 hover:bg-background/20 rounded-full p-0.5"
                >
                  ×
                </button>
              </Badge>
            )}
          </div>
        </Card>
      )}

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleExportHistory}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={handleClearHistory}>
            <Trash2 className="w-4 h-4 mr-2" />
            Clear All
          </Button>
        </div>
      </div>
    </div>
  )
}
