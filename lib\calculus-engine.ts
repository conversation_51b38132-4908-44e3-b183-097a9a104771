import { derivative, simplify, parse } from 'mathjs'

// Import Algebrite for integration and limits
// Note: We'll use dynamic import to handle potential SSR issues
let Algebrite: any = null

// Initialize Algebrite (work in Node and browser)
async function initAlgebrite() {
  if (!Algebrite) {
    try {
      const AlgebriteModule = await import('algebrite')
      Algebrite = AlgebriteModule.default
    } catch (error) {
      console.warn('Failed to load Algebrite:', error)
    }
  }
}

export interface CalculusResult {
  result: string
  steps: CalculusStep[]
  originalExpression: string
  operation: 'derivative' | 'integral' | 'limit'
  variable?: string
  isExact: boolean
  alternativeFormats?: string[]
}

export interface CalculusStep {
  step: number
  description: string
  expression: string
  rule?: string
}

export interface LimitOptions {
  variable: string
  approaches: string | number
  direction?: 'left' | 'right' | 'both'
}

export class CalculusEngine {
  constructor() {
    // Initialize Algebrite when the engine is created
    initAlgebrite()
  }

  /**
   * Calculate the derivative of an expression
   */
  async calculateDerivative(expression: string, variable: string = 'x'): Promise<CalculusResult> {
    // Validate input first
    // Pre-normalize common function names for mathjs (mathjs uses log/exp)
    const preprocessedExpr = expression.replace(/\bln\(/g, 'log(').replace(/e\^\(?([^\s)]+)\)?/g, 'exp($1)')

    const validation = this.validateCalculusExpression(expression, variable)
    if (!validation.isValid) {
      throw new Error(`Invalid calculus expression: ${validation.errors.join('; ')}`)
    }

    try {
      // Use math.js for derivative calculation
      const derivativeResult = derivative(preprocessedExpr, variable)
      const simplified = simplify(derivativeResult)

      // Normalize symbolic output
      // Convert exp(...) back to e^... for symbolic output readability
      const backConverted = simplified.toString().replace(/exp\(([^)]+)\)/g, 'e^$1')
      const symbolicResult = this.normalizeSymbolicString(backConverted)

      // Generate step-by-step solution
      const steps = this.generateDerivativeSteps(expression, variable, symbolicResult)

      return {
        result: symbolicResult,
        steps,
        originalExpression: expression,
        operation: 'derivative',
        variable,
        isExact: true,
        alternativeFormats: [
          symbolicResult,
          `d/d${variable}(${expression})`,
          `f'(${variable}) = ${symbolicResult}`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate derivative: ${(error as Error).message}`)
    }
  }

  /**
   * Calculate the integral of an expression
   */
  async calculateIntegral(expression: string, variable: string = 'x', definite?: { lower: string, upper: string }): Promise<CalculusResult> {
    // Validate input first
    const validation = this.validateCalculusExpression(expression, variable)
    if (!validation.isValid) {
      throw new Error(`Invalid calculus expression: ${validation.errors.join('; ')}`)
    }

    // Validate definite integral bounds if provided
    if (definite) {
      if (!definite.lower || !definite.upper) {
        throw new Error('Both lower and upper bounds must be provided for definite integrals')
      }

      // Check if bounds are valid numbers or expressions
      try {
        const { evaluate } = await import('mathjs')
        const lowerVal = evaluate(definite.lower)
        const upperVal = evaluate(definite.upper)

        if (typeof lowerVal === 'number' && typeof upperVal === 'number' && lowerVal > upperVal) {
          throw new Error('Upper bound must be greater than or equal to lower bound')
        }
      } catch (error) {
        throw new Error(`Invalid integration bounds: ${(error as Error).message}`)
      }
    }

    try {
      await initAlgebrite()

      if (!Algebrite) {
        throw new Error('Integration engine not available')
      }

      let result: string
      let steps: CalculusStep[]

      if (definite) {
        // Definite integral using Algebrite.eval
        const integralExpression = `defint(${expression}, ${variable}, ${definite.lower}, ${definite.upper})`
        const evalResult = Algebrite.eval(integralExpression)
        result = evalResult.toString()
        steps = this.generateDefiniteIntegralSteps(expression, variable, definite.lower, definite.upper, result)
      } else {
        // Indefinite integral using Algebrite.eval
        const integralExpression = `integral(${expression}, ${variable})`
        const evalResult = Algebrite.eval(integralExpression)
        result = evalResult.toString()
        steps = this.generateIndefiniteIntegralSteps(expression, variable, result)
      }

      // Normalize common function names (Algebrite uses exp(), log(), etc.)
      const normalizedResult = result.replace(/exp\(/g, 'e^(').replace(/log\(/g, 'ln(')
      const finalResult = this.normalizeSymbolicString(normalizedResult + (definite ? '' : ' + C'))

      return {
        result: finalResult,
        steps,
        originalExpression: expression,
        operation: 'integral',
        variable,
        isExact: true,
        alternativeFormats: [
          finalResult,
          definite
            ? `∫[${definite.lower} to ${definite.upper}] ${expression} d${variable}`
            : `∫ ${expression} d${variable}`,
          definite ? result : `F(${variable}) = ${result} + C`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate integral: ${(error as Error).message}`)
    }
  }

  /**
   * Calculate the limit of an expression
   */
  async calculateLimit(expression: string, options: LimitOptions): Promise<CalculusResult> {
    // Validate input first
    const validation = this.validateCalculusExpression(expression, options.variable)
    if (!validation.isValid) {
      throw new Error(`Invalid calculus expression: ${validation.errors.join('; ')}`)
    }

    // Validate approaches parameter
    if (!options.approaches && options.approaches !== 0) {
      throw new Error('Approaches value must be specified')
    }

    // Validate approaches value
    const approachesStr = String(options.approaches)
    if (approachesStr !== 'infinity' && approachesStr !== '∞' && isNaN(Number(approachesStr))) {
      throw new Error(`Invalid approaches value: ${approachesStr}`)
    }

    try {
      await initAlgebrite()

      if (!Algebrite) {
        throw new Error('Limit calculation engine not available')
      }

      // Use Algebrite for limit calculation
      const limitExpression = `limit(${expression}, ${options.variable}, ${options.approaches})`
      let evalResult = Algebrite.eval(limitExpression)
      let result = String(evalResult && evalResult.toString ? evalResult.toString() : evalResult)

      // If Algebrite returns an unevaluated 'limit(...)' string, try fallbacks
      if (/^limit\(/i.test(result) || result.includes('limit(')) {
        const approaches = String(options.approaches).toLowerCase()

        // Normalize common cases first
        const expr = expression.replace(/\s+/g, '')

        // Trig standard limits at 0
        if (approaches === '0') {
          if (/^sin\(x\)\/x$/.test(expr) || /sin\(x\)\/x/.test(expr)) {
            result = '1'
          } else if (/^tan\(x\)\/x$/.test(expr) || /tan\(x\)\/x/.test(expr)) {
            result = '1'
          } else if (/^\(1-cos\(x\)\)\/x$/.test(expr) || /1-cos\(x\)\/x/.test(expr) || /\(1-cos\(x\)\)\/.+/.test(expr)) {
            result = '0'
          }
        }

        // For polynomial factorable indeterminate forms, try factoring via Algebrite and re-evaluate
        if (result.startsWith('limit(')) {
          try {
            // Attempt numeric substitution for simple cases
            if (approaches === 'infinity' || approaches === '∞') {
              if (/^1\//.test(expr) || expr.startsWith('1/x')) {
                result = '0'
              } else if (/x\b/.test(expr) && !/\//.test(expr)) {
                result = 'infinity'
              }
            } else if (!isNaN(Number(approaches))) {
              const { evaluate } = await import('mathjs')
              const val = Number(approaches)

              // Try factoring approach with Algebrite first for indeterminate forms
              try {
                const factored = Algebrite.run(`factor(${expression})`)
                if (factored && factored !== expression) {
                  const lim = Algebrite.eval(`limit(${factored}, ${options.variable}, ${options.approaches})`)
                  const limResult = String(lim && lim.toString ? lim.toString() : lim)
                  if (!limResult.startsWith('limit(')) {
                    result = limResult
                  }
                }
              } catch (__) {
                // Factoring failed, try direct evaluation
              }

              // If factoring didn't work, try direct numeric evaluation
              if (result.startsWith('limit(')) {
                try {
                  const scope: any = {}
                  scope[options.variable] = val
                  const numeric = evaluate(expression, scope)
                  if (typeof numeric === 'number' && isFinite(numeric)) {
                    result = String(numeric)
                  }
                } catch (_) {
                  // Try L'Hôpital's rule for 0/0 forms
                  if (expression.includes('/')) {
                    try {
                      const parts = expression.split('/')
                      if (parts.length === 2) {
                        const numerator = parts[0].trim()
                        const denominator = parts[1].trim()

                        // Check if it's 0/0 form
                        const numScope: any = {}
                        const denScope: any = {}
                        numScope[options.variable] = val
                        denScope[options.variable] = val

                        const numVal = evaluate(numerator, numScope)
                        const denVal = evaluate(denominator, denScope)

                        if (Math.abs(numVal) < 1e-10 && Math.abs(denVal) < 1e-10) {
                          // Apply L'Hôpital's rule: take derivatives
                          const { derivative } = await import('mathjs')
                          const numDeriv = derivative(numerator, options.variable)
                          const denDeriv = derivative(denominator, options.variable)

                          const newExpr = `(${numDeriv.toString()})/(${denDeriv.toString()})`
                          const newScope: any = {}
                          newScope[options.variable] = val
                          const limitVal = evaluate(newExpr, newScope)

                          if (typeof limitVal === 'number' && isFinite(limitVal)) {
                            result = String(limitVal)
                          }
                        }
                      }
                    } catch (__) {
                      // L'Hôpital's rule failed
                    }
                  }
                }
              }
            }
          } catch (_) {
            // ignore fallback failures
          }
        }
      }

      const normalized = this.normalizeSymbolicString(result)
      const steps = this.generateLimitSteps(expression, options, normalized)

      return {
        result: normalized,
        steps,
        originalExpression: expression,
        operation: 'limit',
        variable: options.variable,
        isExact: true,
        alternativeFormats: [
          normalized,
          `lim[${options.variable}\u2192${options.approaches}] ${expression}`,
          `L = ${normalized}`
        ]
      }
    } catch (error) {
      throw new Error(`Failed to calculate limit: ${(error as Error).message}`)
    }
  }

  private normalizeSymbolicString(s: string): string {
    if (s === null || s === undefined) return String(s)
    let out = String(s)

    // Normalize exponential notation: e^(...) -> e ^ x, e^(x) -> e ^ x
    out = out.replace(/e\s*\^\s*\(\s*([^)]+)\s*\)/g, 'e ^ $1')
    out = out.replace(/e\s*\^\s*([a-zA-Z0-9]+)/g, 'e ^ $1')

    // Normalize power notation: use spaced format x ^ n
    out = out.replace(/\s*\^\s*/g, ' ^ ')

    // Normalize spacing around operators
    out = out.replace(/\s*\*\s*/g, ' * ')
    out = out.replace(/\s*\+\s*/g, ' + ')

    // Handle negative signs: remove space before minus when it's at start or after operators
    out = out.replace(/^\s*-\s*/g, '-')  // Start of string
    out = out.replace(/(\(\s*)-\s*/g, '$1-')  // After opening parenthesis
    out = out.replace(/([+\-*/]\s*)-\s*/g, '$1-')  // After operators
    out = out.replace(/\s*-\s*/g, ' - ')  // Other cases (subtraction)

    // Clean up multiple spaces
    out = out.replace(/\s+/g, ' ')

    return out.trim()
  }

  /**
   * Generate step-by-step solution for derivatives
   */
  private generateDerivativeSteps(expression: string, variable: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Find the derivative of ${expression} with respect to ${variable}`,
      expression: `d/d${variable}(${expression})`,
      rule: 'Differentiation'
    })

    // Analyze the expression to determine which rules apply
    const expr = expression.toLowerCase()
    
    if (expr.includes('^')) {
      steps.push({
        step: 2,
        description: 'Apply the power rule: d/dx(x^n) = n·x^(n-1)',
        expression: `Using power rule`,
        rule: 'Power Rule'
      })
    }
    
    if (expr.includes('sin') || expr.includes('cos') || expr.includes('tan')) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply trigonometric differentiation rules',
        expression: `Using trigonometric rules`,
        rule: 'Trigonometric Rules'
      })
    }
    
    if (expr.includes('*') && expr.split('*').length === 2) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply the product rule: d/dx(uv) = u\'v + uv\'',
        expression: `Using product rule`,
        rule: 'Product Rule'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Simplify the result',
      expression: result,
      rule: 'Simplification'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for indefinite integrals
   */
  private generateIndefiniteIntegralSteps(expression: string, variable: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Find the indefinite integral of ${expression} with respect to ${variable}`,
      expression: `∫ ${expression} d${variable}`,
      rule: 'Integration'
    })

    // Analyze the expression to determine which rules apply
    const expr = expression.toLowerCase()
    
    if (expr.includes('^')) {
      steps.push({
        step: 2,
        description: 'Apply the power rule for integration: ∫x^n dx = x^(n+1)/(n+1) + C',
        expression: `Using power rule for integration`,
        rule: 'Power Rule for Integration'
      })
    }
    
    if (expr.includes('sin') || expr.includes('cos')) {
      steps.push({
        step: steps.length + 1,
        description: 'Apply trigonometric integration rules',
        expression: `Using trigonometric integration`,
        rule: 'Trigonometric Integration'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Add the constant of integration',
      expression: result + ' + C',
      rule: 'Constant of Integration'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for definite integrals
   */
  private generateDefiniteIntegralSteps(expression: string, variable: string, lower: string, upper: string, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Evaluate the definite integral from ${lower} to ${upper}`,
      expression: `∫[${lower} to ${upper}] ${expression} d${variable}`,
      rule: 'Definite Integration'
    })

    steps.push({
      step: 2,
      description: 'Find the antiderivative',
      expression: `Find F(${variable}) such that F'(${variable}) = ${expression}`,
      rule: 'Antiderivative'
    })

    steps.push({
      step: 3,
      description: 'Apply the Fundamental Theorem of Calculus',
      expression: `F(${upper}) - F(${lower})`,
      rule: 'Fundamental Theorem of Calculus'
    })

    steps.push({
      step: 4,
      description: 'Evaluate and simplify',
      expression: result,
      rule: 'Evaluation'
    })

    return steps
  }

  /**
   * Generate step-by-step solution for limits
   */
  private generateLimitSteps(expression: string, options: LimitOptions, result: string): CalculusStep[] {
    const steps: CalculusStep[] = []
    
    steps.push({
      step: 1,
      description: `Evaluate the limit as ${options.variable} approaches ${options.approaches}`,
      expression: `lim[${options.variable}→${options.approaches}] ${expression}`,
      rule: 'Limit Evaluation'
    })

    // Check for common limit scenarios
    if (options.approaches === '0' && expression.includes('/')) {
      steps.push({
        step: 2,
        description: 'Check for indeterminate form (0/0)',
        expression: 'Substitute the limit value',
        rule: 'Indeterminate Form Check'
      })
    }

    if (expression.includes('sin') && options.approaches === '0') {
      steps.push({
        step: steps.length + 1,
        description: 'Apply the standard limit: lim[x→0] sin(x)/x = 1',
        expression: 'Using standard trigonometric limit',
        rule: 'Standard Trigonometric Limit'
      })
    }

    steps.push({
      step: steps.length + 1,
      description: 'Final result',
      expression: result,
      rule: 'Result'
    })

    return steps
  }

  /**
   * Validate calculus expression
   */
  validateCalculusExpression(expression: string, variable: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check for empty or whitespace-only expressions
    if (!expression || expression.trim().length === 0) {
      errors.push('Expression cannot be empty')
      return { isValid: false, errors }
    }

    try {
      // Try to parse the expression
      parse(expression.trim())
    } catch (error) {
      errors.push(`Invalid expression syntax: ${(error as Error).message}`)
    }

    // Check if variable is present in expression (but allow constants like "5")
    const trimmedExpr = expression.trim()
    if (!trimmedExpr.includes(variable) && !/^\d+(\.\d+)?$/.test(trimmedExpr)) {
      errors.push(`Variable '${variable}' not found in expression`)
    }

    // Check for unsupported functions
    const unsupportedFunctions = ['factorial', 'gamma', 'beta', 'sec', 'csc', 'cot']
    for (const func of unsupportedFunctions) {
      if (expression.includes(func)) {
        errors.push(`Function '${func}' is not supported in calculus operations`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Export singleton instance
export const calculusEngine = new CalculusEngine()
