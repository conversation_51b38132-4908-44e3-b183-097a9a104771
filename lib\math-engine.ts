// Core mathematical engine using math.js for computation

import { evaluate, parse, simplify } from "mathjs"
import type { Calcula<PERSON>R<PERSON>ult, ParsedExpression, ValidationResult, Step, ComputationError, MathematicalIntent } from "./types"
import { calculusEngine } from "./calculus-engine"

export class MathEngine {
  /**
   * Evaluates a mathematical expression and returns the result
   */
  evaluate(expression: string): CalculationResult {
    try {
      // Normalize the expression first
      const normalizedExpression = this.normalizeExpression(expression)
      const result = evaluate(normalizedExpression)

      // Check for invalid results
      if (result === undefined || result === null) {
        throw new Error("Expression evaluation returned undefined result")
      }

      return {
        value: result,
        formattedValue: this.formatResult(result),
        isExact: this.isExactResult(result),
        alternativeFormats: this.getAlternativeFormats(result),
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Parses and validates a mathematical expression
   */
  parseExpression(input: string): ParsedExpression {
    try {
      const normalized = this.normalizeExpression(input)
      const parsed = parse(normalized)

      return {
        original: input,
        normalized,
        tokens: this.tokenize(normalized),
        type: this.detectExpressionType(normalized),
        complexity: this.assessComplexity(normalized),
      }
    } catch (error) {
      throw new Error(`Failed to parse expression: ${input}`)
    }
  }

  /**
   * Validates mathematical expression syntax
   */
  validateExpression(expression: string): ValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Basic checks
    if (!expression || expression.trim().length === 0) {
      errors.push("Expression cannot be empty")
      return { isValid: false, errors, warnings }
    }

    try {
      const normalized = this.normalizeExpression(expression)
      parse(normalized)
    } catch (error) {
      errors.push(`Invalid syntax: ${(error as Error).message}`)
    }

    // Check for common issues
    if (expression.includes("/0") && !expression.includes("/0.")) {
      errors.push("Division by zero detected")
    }

    if (expression.match(/\d+[a-zA-Z]/)) {
      warnings.push("Implicit multiplication detected - consider using * operator")
    }

    // Check for unmatched parentheses
    const openParens = (expression.match(/\(/g) || []).length
    const closeParens = (expression.match(/\)/g) || []).length
    if (openParens !== closeParens) {
      errors.push("Unmatched parentheses")
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  }

  /**
   * Simplifies a mathematical expression
   */
  simplifyExpression(expression: string): string {
    try {
      const simplified = simplify(expression)
      return simplified.toString()
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Generates step-by-step solution for basic operations
   */
  generateSteps(expression: string, operation: string): Step[] {
    const steps: Step[] = []

    try {
      // For now, create a basic step for the evaluation
      // This will be expanded with more sophisticated step generation
      const result = evaluate(expression)

      steps.push({
        id: "1",
        description: `Evaluate ${expression}`,
        expression: expression,
        rule: "Direct evaluation",
        explanation: `Computing the value of ${expression}`,
      })

      steps.push({
        id: "2",
        description: "Result",
        expression: result.toString(),
        rule: "Final answer",
        explanation: `The result is ${result}`,
      })

      return steps
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Processes mathematical intent and returns calculation result
   */
  async processIntent(intent: MathematicalIntent): Promise<CalculationResult> {
    switch (intent.operation) {
      case "evaluate":
        return this.evaluate(intent.expression)
      case "solve":
        return this.solveEquation(intent.expression, intent.variables?.[0] || "x")
      case "differentiate":
  return await this.calculateDerivative(intent.expression, intent.variables?.[0] || "x")
      case "integrate":
        return await this.calculateIntegral(intent.expression, intent.variables?.[0] || "x")
      case "definiteIntegrate":
        return await this.calculateDefiniteIntegral(
          intent.expression,
          intent.variables?.[0] || "x",
          intent.bounds!
        )
      case "limit":
        return await this.calculateLimit(intent.expression, {
          variable: intent.variables?.[0] || "x",
          approaches: intent.approaches || "0"
        })
      default:
        throw new Error(`Unsupported operation: ${intent.operation}`)
    }
  }

  /**
   * Calculate derivative using calculus engine
   */
  async calculateDerivative(expression: string, variable: string = "x"): Promise<CalculationResult> {
    try {
      const result = await calculusEngine.calculateDerivative(expression, variable)

      if (!result || typeof result.result !== 'string') {
        throw new Error('Invalid derivative result')
      }

      const formattedSymbolic = this.normalizeSymbolic(result.result)

      return {
        value: result.result, // Keep as symbolic string
        formattedValue: formattedSymbolic, // Keep as symbolic string
        isExact: result.isExact,
        steps: result.steps.map((step, index) => ({
          id: `step-${index + 1}`,
          expression: step.expression,
          rule: step.rule || step.description,
          description: step.description,
          explanation: step.description
        })),
        alternativeFormats: result.alternativeFormats
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Calculate integral using calculus engine
   */
  async calculateIntegral(expression: string, variable: string = "x"): Promise<CalculationResult> {
    try {
      const result = await calculusEngine.calculateIntegral(expression, variable)

      if (!result || typeof result.result !== 'string') {
        throw new Error('Invalid integral result')
      }

      const formattedSymbolic = this.normalizeSymbolic(result.result)

      return {
        value: result.result,
        formattedValue: formattedSymbolic,
        isExact: result.isExact,
        steps: result.steps.map((step, index) => ({
          id: `integral-step-${index + 1}`,
          expression: step.expression,
          rule: step.rule || step.description,
          description: step.description,
          explanation: step.description
        })),
        alternativeFormats: result.alternativeFormats
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Calculate definite integral using calculus engine
   */
  async calculateDefiniteIntegral(expression: string, variable: string, bounds: { lower: string, upper: string }): Promise<CalculationResult> {
    try {
      const result = await calculusEngine.calculateIntegral(expression, variable, bounds)

      if (!result || typeof result.result !== 'string') {
        throw new Error('Invalid definite integral result')
      }

      const formattedSymbolic = this.normalizeSymbolic(result.result)

      return {
        value: result.result,
        formattedValue: formattedSymbolic,
        isExact: result.isExact,
        steps: result.steps.map((step, index) => ({
          id: `definite-integral-step-${index + 1}`,
          expression: step.expression,
          rule: step.rule || step.description,
          description: step.description,
          explanation: step.description
        })),
        alternativeFormats: result.alternativeFormats
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Calculate limit using calculus engine
   */
  async calculateLimit(expression: string, options: { variable: string, approaches: string }): Promise<CalculationResult> {
    try {
      const result = await calculusEngine.calculateLimit(expression, {
        variable: options.variable,
        approaches: options.approaches
      })

      if (!result || typeof result.result !== 'string') {
        throw new Error('Invalid limit result')
      }

      const formattedSymbolic = this.normalizeSymbolic(result.result)

      return {
        value: result.result,
        formattedValue: formattedSymbolic,
        isExact: result.isExact,
        steps: result.steps.map((step, index) => ({
          id: `limit-step-${index + 1}`,
          expression: step.expression,
          rule: step.rule || step.description,
          description: step.description,
          explanation: step.description
        })),
        alternativeFormats: result.alternativeFormats
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  /**
   * Solve equation (placeholder implementation)
   */
  solveEquation(expression: string, variable: string = "x"): CalculationResult {
    // This is a placeholder - full equation solving would require more sophisticated implementation
    try {
      // For now, just try to evaluate if it's a simple expression
      const result = evaluate(expression)

      return {
        value: result,
        formattedValue: this.formatResult(result),
        isExact: this.isExactResult(result),
        alternativeFormats: this.getAlternativeFormats(result),
        steps: [{
          id: "solve-step-1",
          expression: expression,
          rule: "Evaluation",
          description: "Evaluated expression",
          explanation: `Evaluated ${expression} = ${result}`
        }]
      }
    } catch (error) {
      throw this.createComputationError(error as Error, expression)
    }
  }

  // Private helper methods
  private normalizeExpression(expression: string): string {
    return expression
  .replace(/\s+/g, "") // Remove whitespace
      .replace(/×/g, "*") // Replace × with *
      .replace(/÷/g, "/") // Replace ÷ with /
      .replace(/π/g, "pi") // Replace π with pi
      .replace(/√/g, "sqrt") // Replace √ with sqrt
  .replace(/\bln\(/g, 'log(') // normalize ln(x) to log(x) for mathjs
  .replace(/e\^\(/g, 'exp(') // normalize e^(...) to exp(...)
  }

  private tokenize(expression: string): any[] {
    // Basic tokenization - this would be more sophisticated in a full implementation
    return []
  }

  private detectExpressionType(expression: string): any {
    if (expression.match(/sin|cos|tan/)) return "trigonometric"
    if (expression.match(/log|ln/)) return "logarithmic"
    if (expression.match(/[a-zA-Z]/)) return "algebraic"
    return "arithmetic"
  }

  private assessComplexity(expression: string): "basic" | "intermediate" | "advanced" {
    const operators = (expression.match(/[+\-*/^]/g) || []).length
    const functions = (expression.match(/sin|cos|tan|log|sqrt/g) || []).length

    if (functions > 0 || operators > 3) return "advanced"
    if (operators > 1) return "intermediate"
    return "basic"
  }

  private formatResult(result: any): string {
    if (typeof result === "number") {
      // Format numbers with appropriate precision
      if (Number.isInteger(result)) {
        return result.toString()
      }
      return result.toFixed(6).replace(/\.?0+$/, "")
    }
    return result.toString()
  }

  private isExactResult(result: any): boolean {
    return typeof result === "number" && Number.isInteger(result)
  }

  private getAlternativeFormats(result: any): string[] {
    const formats: string[] = []

    if (typeof result === "number") {
      formats.push(result.toExponential(3))
      if (result !== 0) {
        formats.push((1 / result).toString()) // Reciprocal
      }
    }

    return formats
  }

  /**
   * Normalize symbolic result strings for consistent formatting used in tests/UI.
   * Example: '3 * x ^ 2' -> '3 * x^2'
   */
  private normalizeSymbolic(symbolic: any): string {
    if (symbolic === null || symbolic === undefined) return String(symbolic)
    let s = String(symbolic)

    // Remove spaces around caret for exponent formatting
    s = s.replace(/\s*\^\s*/g, '^')

    // Collapse multiple spaces to single space
    s = s.replace(/\s+/g, ' ')

    // Remove space between variable and exponent number like 'x ^2' -> 'x^2'
    s = s.replace(/x\s*\^(\S+)/g, 'x^$1')

    return s.trim()
  }

  private createComputationError(error: Error, expression: string): ComputationError {
    const computationError = new Error(error.message) as ComputationError
    computationError.type = "domain"
    computationError.context = expression
    return computationError
  }
}

// Export singleton instance
export const mathEngine = new MathEngine()
