// Natural Language Processing for mathematical expressions

import type { MathematicalIntent, MathEntity } from "./types"

export class NLPProcessor {
  private patterns = {
    // Basic arithmetic patterns
    addition: [
      /what is (\d+(?:\.\d+)?) (?:plus|\+) (\d+(?:\.\d+)?)/i,
      /add (\d+(?:\.\d+)?) (?:and|to) (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?) (?:plus|\+) (\d+(?:\.\d+)?)/i,
    ],
    subtraction: [
      /what is (\d+(?:\.\d+)?) (?:minus|-) (\d+(?:\.\d+)?)/i,
      /subtract (\d+(?:\.\d+)?) from (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?) (?:minus|-) (\d+(?:\.\d+)?)/i,
    ],
    multiplication: [
      /what is (\d+(?:\.\d+)?) (?:times|multiplied by|\*) (\d+(?:\.\d+)?)/i,
      /multiply (\d+(?:\.\d+)?) (?:by|and) (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?) (?:times|\*) (\d+(?:\.\d+)?)/i,
    ],
    division: [
      /what is (\d+(?:\.\d+)?) (?:divided by|over|\/) (\d+(?:\.\d+)?)/i,
      /divide (\d+(?:\.\d+)?) by (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?) (?:divided by|\/) (\d+(?:\.\d+)?)/i,
    ],
    // Advanced operations
    squareRoot: [
      /(?:what is )?(?:the )?square root of (\d+(?:\.\d+)?)/i,
      /sqrt\((\d+(?:\.\d+)?)\)/i,
      /√(\d+(?:\.\d+)?)/i,
    ],
    power: [
      /(?:what is )?(\d+(?:\.\d+)?) (?:to the power of|raised to|[\^]) (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?)[\^](\d+(?:\.\d+)?)/i,
    ],
    // Percentage calculations
    percentage: [
      /(?:what is )?(\d+(?:\.\d+)?)%? (?:of|percent of) (\d+(?:\.\d+)?)/i,
      /(?:calculate )?(\d+(?:\.\d+)?)% (?:of|from) (\d+(?:\.\d+)?)/i,
      /(\d+(?:\.\d+)?)% \* (\d+(?:\.\d+)?)/i,
    ],
    // Equation solving
    solve: [
      /solve (?:the equation )?(.+?)(?:\s*=\s*0)?(?:\s+for\s+(\w+))?/i,
      /find (?:the )?(?:value of |solution to )?(.+?)(?:\s+for\s+(\w+))?/i,
    ],
    // Calculus
    derivative: [
      // Use greedy captures for the main derivative patterns so we don't accidentally
      // capture a single character when the non-greedy version matches too little.
      /(?:find )?(?:the )?derivative of (.+)(?:\s+with respect to\s+(\w+))?/i,
      /differentiate (.+)(?:\s+with respect to\s+(\w+))?/i,
      /d\/d(\w+)\s*\((.+)\)/i,
      /d\/d(\w+)\s*(.+)/i,
      /(?:what is )?(?:the )?derivative of (.+)/i,
      /derive (.+)(?:\s+with respect to\s+(\w+))?/i,
    ],
    integral: [
      /(?:find )?(?:the )?integral of (.+)(?:\s+with respect to\s+(\w+))?/i,
      /integrate (.+)(?:\s+with respect to\s+(\w+))?/i,
      /∫(.+)d(\w+)/i,
      /(?:what is )?(?:the )?integral of (.+)/i,
      /antiderivative of (.+)(?:\s+with respect to\s+(\w+))?/i,
      /(?:find )?(?:the )?antiderivative of (.+)/i,
    ],
    // Definite integrals
    definiteIntegral: [
      /(?:evaluate )?(?:the )?(?:definite )?integral of (.+) from (.+) to (.+)(?:\s+with respect to\s+(\w+))?/i,
      /∫\[(.+) to (.+)\]\s*(.+)d(\w+)/i,
      /integrate (.+) from (.+) to (.+)(?:\s+with respect to\s+(\w+))?/i,
    ],
    // Limits
    limit: [
      /(?:find )?(?:the )?limit of (.+) as (\w+) approaches (.+)/i,
      /lim(?:it)?\s*(?:\[(\w+)→(.+)\])?\s*(.+)/i,
      /(?:what is )?(?:the )?limit as (\w+) goes to (.+) of (.+)/i,
      /limit of (.+) when (\w+) approaches (.+)/i,
    ],
  }

  /**
   * Processes natural language input and extracts mathematical intent
   */
  parseNaturalLanguage(text: string): MathematicalIntent {
    const normalizedText = text.toLowerCase().trim()

    // First, check if this is actually a pure mathematical expression
    // If it contains only numbers, operators, and functions, don't try to parse it as natural language
    const isPureMath = this.isPureMathematicalExpression(text)
    if (isPureMath) {
      return {
        operation: "evaluate",
        expression: text.trim(),
        variables: this.extractVariables(text),
      }
    }

    // Check for different operation types. Prefer the pattern that matches the
    // longest substring (more specific match) in case multiple patterns apply.
    let best: { operation: string; match: RegExpMatchArray | null; length: number } | null = null

    for (const [operation, patterns] of Object.entries(this.patterns)) {
      for (const pattern of patterns) {
        const match = normalizedText.match(pattern)
        if (match) {
          const len = (match[0] || '').length
          if (!best || len > best.length) {
            best = { operation, match, length: len }
          }
        }
      }
    }

    if (best && best.match) {
      return this.createIntent(best.operation, best.match, normalizedText)
    }

    // If no specific pattern matches, try to extract a general mathematical expression
    const mathExpression = this.extractMathExpression(normalizedText)
    if (mathExpression) {
      return {
        operation: "evaluate",
        expression: mathExpression,
        variables: this.extractVariables(mathExpression),
      }
    }

    throw new Error(`Could not understand the mathematical expression: "${text}"`)
  }

  /**
   * Extracts mathematical entities from text
   */
  extractMathematicalEntities(text: string): MathEntity[] {
    const entities: MathEntity[] = []

    // Extract numbers
    const numbers = text.match(/\d+(?:\.\d+)?/g) || []
    numbers.forEach((num) => {
      entities.push({
        type: "number",
        value: num,
        confidence: 0.9,
      })
    })

    // Extract operations
    const operations = text.match(/\b(?:plus|minus|times|divided by|add|subtract|multiply|divide)\b/gi) || []
    operations.forEach((op) => {
      entities.push({
        type: "operation",
        value: this.normalizeOperation(op),
        confidence: 0.8,
      })
    })

    // Extract functions
    const functions = text.match(/\b(?:sin|cos|tan|log|ln|sqrt|square root)\b/gi) || []
    functions.forEach((func) => {
      entities.push({
        type: "function",
        value: this.normalizeFunction(func),
        confidence: 0.85,
      })
    })

    return entities
  }

  /**
   * Converts natural language intent to mathematical expression
   */
  convertToMathExpression(intent: MathematicalIntent): string {
    return intent.expression
  }

  /**
   * Detects the type of input (natural language vs mathematical notation)
   */
  detectInputType(input: string): "natural" | "mathematical" | "mixed" {
    const hasNaturalLanguage = /\b(?:what is|solve|find|calculate|derivative|integral)\b/i.test(input)
    const hasMathSymbols = /[+\-*/^()=∫∂√π]/i.test(input)

    if (hasNaturalLanguage && hasMathSymbols) return "mixed"
    if (hasNaturalLanguage) return "natural"
    return "mathematical"
  }

  // Private helper methods

  /**
   * Determines if the input is a pure mathematical expression (no natural language)
   */
  private isPureMathematicalExpression(text: string): boolean {
    const trimmed = text.trim()

    // Check if it contains natural language keywords
    const hasNaturalLanguage = /\b(?:what is|solve|find|calculate|derivative|integral|add|subtract|multiply|divide|plus|minus|times|divided by|square root of|percent of)\b/i.test(trimmed)

    if (hasNaturalLanguage) {
      return false
    }

    // Check if it's primarily mathematical notation
    // Allow: numbers, operators, mathematical functions, parentheses, constants
    const mathFunctions = /\b(?:sin|cos|tan|asin|acos|atan|sinh|cosh|tanh|log|ln|log10|log2|exp|sqrt|cbrt|abs|ceil|floor|round|max|min|pow)\b/i
    const hasOperators = /[+\-*/^=()]/.test(trimmed)
    const hasFunctions = mathFunctions.test(trimmed)
    const hasNumbers = /\d/.test(trimmed)
    const hasConstants = /\b(?:pi|e|i)\b/i.test(trimmed)

    // Remove known mathematical elements and see what's left
    let cleaned = trimmed
      .replace(/\b(?:sin|cos|tan|asin|acos|atan|sinh|cosh|tanh|log|ln|log10|log2|exp|sqrt|cbrt|abs|ceil|floor|round|max|min|pow)\b/gi, '')
      .replace(/\b(?:pi|e|i)\b/gi, '')
      .replace(/[0-9+\-*/^().,\s=!%]/g, '')

    // If there are non-mathematical characters left, it's probably natural language
    const hasNonMathChars = cleaned.length > 0

    // It's pure math if it has mathematical elements and no natural language
    return (hasOperators || hasFunctions || hasNumbers || hasConstants) && !hasNonMathChars
  }

  private createIntent(operation: string, match: RegExpMatchArray, text: string): MathematicalIntent {
    switch (operation) {
      case "addition":
        return {
          operation: "evaluate",
          expression: `${match[1]} + ${match[2]}`,
        }
      case "subtraction":
        return {
          operation: "evaluate",
          expression: `${match[1]} - ${match[2]}`,
        }
      case "multiplication":
        return {
          operation: "evaluate",
          expression: `${match[1]} * ${match[2]}`,
        }
      case "division":
        return {
          operation: "evaluate",
          expression: `${match[1]} / ${match[2]}`,
        }
      case "squareRoot":
        return {
          operation: "evaluate",
          expression: `sqrt(${match[1]})`,
        }
      case "power":
        return {
          operation: "evaluate",
          expression: `${match[1]}^${match[2]}`,
        }
      case "percentage":
        return {
          operation: "evaluate",
          expression: `${match[1]} * ${match[2]} / 100`,
        }
      case "solve":
        return {
          operation: "solve",
          expression: match[1],
          variables: match[2] ? [match[2]] : this.extractVariables(match[1]),
        }
      case "derivative":
        // Handle different derivative pattern matches
        if (match[0].includes('d/d')) {
          // Pattern: d/dx(expression) or d/dx expression
          return {
            operation: "differentiate",
            expression: match[2] || match[1],
            variables: [match[1]],
          }
        } else {
          // Pattern: derivative of expression [with respect to variable]
          return {
            operation: "differentiate",
            expression: match[1],
            variables: match[2] ? [match[2]] : ["x"],
          }
        }
      case "integral":
        return {
          operation: "integrate",
          expression: match[1],
          variables: match[2] ? [match[2]] : ["x"],
        }
      case "definiteIntegral":
        // Patterns vary; choose branch depending on matched pattern text
        let di_expr = ''
        let di_lower = ''
        let di_upper = ''
        let di_var = 'x'

        const raw = match[0] || ''
        if (raw.includes('∫[') || raw.includes('to') && raw.includes(']')) {
          // pattern: ∫[L to U] EXPR dVAR  -> match[1]=lower, match[2]=upper, match[3]=expr, match[4]=var
          di_lower = match[1] || ''
          di_upper = match[2] || ''
          di_expr = match[3] || ''
          di_var = match[4] || 'x'
        } else if (/from\s+/i.test(raw) || /integrate\s+.+\s+from/i.test(raw)) {
          // pattern: integral of EXPR from L to U -> match[1]=expr, match[2]=lower, match[3]=upper, match[4]=var?
          di_expr = match[1] || ''
          di_lower = match[2] || ''
          di_upper = match[3] || ''
          di_var = match[4] || 'x'
        } else {
          // fallback best effort
          di_expr = match[3] || match[1] || ''
          di_lower = match[1] || match[2] || ''
          di_upper = match[2] || match[3] || ''
          di_var = match[4] || 'x'
        }

        return {
          operation: "definiteIntegrate",
          expression: di_expr.trim(),
          variables: [di_var.trim()],
          bounds: {
            lower: di_lower.trim(),
            upper: di_upper.trim(),
          },
        }
      case "limit":
        // Heuristic-driven extraction for different limit pattern captures
        // Known forms:
        // - "limit of EXPR as VAR approaches VAL" -> match[1]=EXPR, match[2]=VAR, match[3]=VAL
        // - "lim[(VAR)→(VAL)] EXPR" -> match[1]=VAR, match[2]=VAL, match[3]=EXPR
        // - "what is the limit as VAR goes to VAL of EXPR" -> match[1]=VAR, match[2]=VAL, match[3]=EXPR
        // - "limit of EXPR when VAR approaches VAL" -> match[1]=EXPR, match[2]=VAR, match[3]=VAL

        let lim_expr = ''
        let lim_var = 'x'
        let lim_approaches: any = '0'

        const rawText = match[0] || ''

        // If first capture is a single-letter variable, treat that as var
        if (match[1] && /^[a-zA-Z]$/.test(match[1].trim()) && match[3]) {
          lim_var = match[1].trim()
          lim_approaches = match[2] ? match[2].trim() : (match[2] || '')
          lim_expr = match[3] ? match[3].trim() : ''
        } else if (match[2] && match[3] && /^[a-zA-Z]$/.test(match[2].trim())) {
          // form: match[1]=expr, match[2]=var, match[3]=approaches
          lim_expr = match[1] ? match[1].trim() : ''
          lim_var = match[2].trim()
          lim_approaches = match[3].trim()
        } else if (match[1] && match[2] && match[3] && /→/.test(rawText)) {
          // lim[(v)→(a)] expr
          lim_var = match[1].trim()
          lim_approaches = match[2].trim()
          lim_expr = match[3].trim()
        } else if (match[1] && match[2] && match[3] && /goes to|go to|approaches/i.test(rawText)) {
          // what is the limit as v goes to a of expr
          lim_var = match[1].trim()
          lim_approaches = match[2].trim()
          lim_expr = match[3].trim()
        } else {
          // fallback: try to assign by common ordering
          lim_expr = (match[1] && /[()+\-*/^]/.test(match[1])) ? match[1].trim() : (match[3] ? match[3].trim() : match[1] || '')
          // find a single-letter variable in captures
          const maybeVar = [match[1], match[2], match[3]].find(m => m && /^[a-zA-Z]$/.test(m.trim()))
          if (maybeVar) lim_var = maybeVar.trim()
          const maybeApproach = [match[1], match[2], match[3]].find(m => m && (m.trim().toLowerCase() === 'infinity' || !/^[a-zA-Z]$/.test(m.trim())))
          if (maybeApproach) lim_approaches = maybeApproach.trim()
        }

        return {
          operation: "limit",
          expression: lim_expr,
          variables: [lim_var],
          approaches: lim_approaches,
        }
      default:
        return {
          operation: "evaluate",
          expression: text,
        }
    }
  }

  private extractMathExpression(text: string): string | null {
    // Remove common natural language words and extract mathematical parts
    const cleaned = text
      .replace(/\b(?:what is|calculate|find|the|of|a)\b/gi, "")
      .replace(/\s+/g, " ")
      .trim()

    // Check if the remaining text looks like a mathematical expression
    if (/^[\d+\-*/^().\s]+$/.test(cleaned) || /[a-zA-Z]/.test(cleaned)) {
      return cleaned
    }

    return null
  }

  private extractVariables(expression: string): string[] {
    const variables = expression.match(/\b[a-zA-Z]\b/g) || []
    return [...new Set(variables)] // Remove duplicates
  }

  private normalizeOperation(operation: string): string {
    const opMap: Record<string, string> = {
      plus: "+",
      add: "+",
      minus: "-",
      subtract: "-",
      times: "*",
      multiply: "*",
      "divided by": "/",
      divide: "/",
    }
    return opMap[operation.toLowerCase()] || operation
  }

  private normalizeFunction(func: string): string {
    const funcMap: Record<string, string> = {
      "square root": "sqrt",
      sine: "sin",
      cosine: "cos",
      tangent: "tan",
      logarithm: "log",
      "natural log": "ln",
    }
    return funcMap[func.toLowerCase()] || func.toLowerCase()
  }
}

// Export singleton instance
export const nlpProcessor = new NLPProcessor()
