#!/usr/bin/env tsx

/**
 * Comprehensive Calculus Test Runner
 * 
 * This script runs all calculus tests and provides detailed reporting
 * to ensure all calculus functionality works correctly.
 */

import { execSync } from 'child_process'
import { readFileSync, writeFileSync } from 'fs'
import { join } from 'path'

interface TestResult {
  suite: string
  passed: number
  failed: number
  total: number
  duration: number
  errors: string[]
}

interface TestReport {
  timestamp: string
  totalTests: number
  totalPassed: number
  totalFailed: number
  totalDuration: number
  suites: TestResult[]
  summary: string
  criticalTests: {
    symbolicPreservation: boolean
    derivativeAccuracy: boolean
    integrationAccuracy: boolean
    limitAccuracy: boolean
    nlpIntegration: boolean
    errorHandling: boolean
  }
}

class CalculusTestRunner {
  private testSuites = [
    'derivatives',
    'integrals', 
    'limits',
    'symbolic-preservation',
    'error-handling',
    'nlp-integration',
  ]

  async runAllTests(): Promise<TestReport> {
    console.log('🧮 Starting Comprehensive Calculus Test Suite...\n')
    
    const startTime = Date.now()
    const results: TestResult[] = []
    
    for (const suite of this.testSuites) {
      console.log(`📝 Running ${suite} tests...`)
      const result = await this.runTestSuite(suite)
      results.push(result)
      
      if (result.failed > 0) {
        console.log(`❌ ${suite}: ${result.failed} failed tests`)
        result.errors.forEach(error => console.log(`   ${error}`))
      } else {
        console.log(`✅ ${suite}: All ${result.passed} tests passed`)
      }
      console.log()
    }
    
    const endTime = Date.now()
    const totalDuration = endTime - startTime
    
    const report = this.generateReport(results, totalDuration)
    await this.saveReport(report)
    this.printSummary(report)
    
    return report
  }

  private async runTestSuite(suite: string): Promise<TestResult> {
    const startTime = Date.now()
    
    try {
      const output = execSync(
        `npx vitest run tests/calculus/${suite}.test.ts --reporter=json`,
        { encoding: 'utf-8', cwd: process.cwd() }
      )
      
      const result = JSON.parse(output)
      const endTime = Date.now()
      
      return {
        suite,
        passed: result.numPassedTests || 0,
        failed: result.numFailedTests || 0,
        total: result.numTotalTests || 0,
        duration: endTime - startTime,
        errors: result.testResults?.flatMap((tr: any) => 
          tr.assertionResults?.filter((ar: any) => ar.status === 'failed')
            .map((ar: any) => ar.title)
        ) || []
      }
    } catch (error) {
      const endTime = Date.now()
      
      return {
        suite,
        passed: 0,
        failed: 1,
        total: 1,
        duration: endTime - startTime,
        errors: [`Suite failed to run: ${error.message}`]
      }
    }
  }

  private generateReport(results: TestResult[], totalDuration: number): TestReport {
    const totalTests = results.reduce((sum, r) => sum + r.total, 0)
    const totalPassed = results.reduce((sum, r) => sum + r.passed, 0)
    const totalFailed = results.reduce((sum, r) => sum + r.failed, 0)
    
    const criticalTests = {
      symbolicPreservation: this.getCriticalTestStatus(results, 'symbolic-preservation'),
      derivativeAccuracy: this.getCriticalTestStatus(results, 'derivatives'),
      integrationAccuracy: this.getCriticalTestStatus(results, 'integrals'),
      limitAccuracy: this.getCriticalTestStatus(results, 'limits'),
      nlpIntegration: this.getCriticalTestStatus(results, 'nlp-integration'),
      errorHandling: this.getCriticalTestStatus(results, 'error-handling'),
    }
    
    const allCriticalPassed = Object.values(criticalTests).every(status => status)
    
    let summary = ''
    if (totalFailed === 0) {
      summary = `🎉 ALL TESTS PASSED! Calculus functionality is working correctly.`
    } else if (allCriticalPassed) {
      summary = `⚠️ Some tests failed, but all critical calculus functionality is working.`
    } else {
      summary = `❌ CRITICAL TESTS FAILED! Calculus functionality has issues that need attention.`
    }
    
    return {
      timestamp: new Date().toISOString(),
      totalTests,
      totalPassed,
      totalFailed,
      totalDuration,
      suites: results,
      summary,
      criticalTests
    }
  }

  private getCriticalTestStatus(results: TestResult[], suiteName: string): boolean {
    const suite = results.find(r => r.suite === suiteName)
    return suite ? suite.failed === 0 : false
  }

  private async saveReport(report: TestReport): Promise<void> {
    const reportPath = join(process.cwd(), 'test-reports', 'calculus-test-report.json')
    const htmlReportPath = join(process.cwd(), 'test-reports', 'calculus-test-report.html')
    
    // Ensure directory exists
    execSync('mkdir -p test-reports', { cwd: process.cwd() })
    
    // Save JSON report
    writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    // Generate HTML report
    const htmlReport = this.generateHtmlReport(report)
    writeFileSync(htmlReportPath, htmlReport)
    
    console.log(`📊 Test report saved to: ${reportPath}`)
    console.log(`🌐 HTML report saved to: ${htmlReportPath}`)
  }

  private generateHtmlReport(report: TestReport): string {
    const passRate = ((report.totalPassed / report.totalTests) * 100).toFixed(1)
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Calculus Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .summary { margin: 20px 0; }
        .suite { margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .passed { background: #d4edda; }
        .failed { background: #f8d7da; }
        .critical { background: #fff3cd; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9ecef; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧮 Calculus Test Report</h1>
        <p><strong>Generated:</strong> ${report.timestamp}</p>
        <p><strong>Summary:</strong> ${report.summary}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <strong>Total Tests:</strong> ${report.totalTests}
        </div>
        <div class="metric">
            <strong>Passed:</strong> ${report.totalPassed}
        </div>
        <div class="metric">
            <strong>Failed:</strong> ${report.totalFailed}
        </div>
        <div class="metric">
            <strong>Pass Rate:</strong> ${passRate}%
        </div>
        <div class="metric">
            <strong>Duration:</strong> ${(report.totalDuration / 1000).toFixed(2)}s
        </div>
    </div>
    
    <h2>Critical Test Status</h2>
    <div class="critical">
        <p><strong>Symbolic Preservation:</strong> ${report.criticalTests.symbolicPreservation ? '✅ PASS' : '❌ FAIL'}</p>
        <p><strong>Derivative Accuracy:</strong> ${report.criticalTests.derivativeAccuracy ? '✅ PASS' : '❌ FAIL'}</p>
        <p><strong>Integration Accuracy:</strong> ${report.criticalTests.integrationAccuracy ? '✅ PASS' : '❌ FAIL'}</p>
        <p><strong>Limit Accuracy:</strong> ${report.criticalTests.limitAccuracy ? '✅ PASS' : '❌ FAIL'}</p>
        <p><strong>NLP Integration:</strong> ${report.criticalTests.nlpIntegration ? '✅ PASS' : '❌ FAIL'}</p>
        <p><strong>Error Handling:</strong> ${report.criticalTests.errorHandling ? '✅ PASS' : '❌ FAIL'}</p>
    </div>
    
    <h2>Test Suite Results</h2>
    ${report.suites.map(suite => `
        <div class="suite ${suite.failed === 0 ? 'passed' : 'failed'}">
            <h3>${suite.suite}</h3>
            <p><strong>Passed:</strong> ${suite.passed} | <strong>Failed:</strong> ${suite.failed} | <strong>Total:</strong> ${suite.total}</p>
            <p><strong>Duration:</strong> ${(suite.duration / 1000).toFixed(2)}s</p>
            ${suite.errors.length > 0 ? `
                <h4>Errors:</h4>
                <ul>
                    ${suite.errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
            ` : ''}
        </div>
    `).join('')}
</body>
</html>
    `
  }

  private printSummary(report: TestReport): void {
    console.log('\n' + '='.repeat(60))
    console.log('📊 CALCULUS TEST SUMMARY')
    console.log('='.repeat(60))
    console.log(`Total Tests: ${report.totalTests}`)
    console.log(`Passed: ${report.totalPassed}`)
    console.log(`Failed: ${report.totalFailed}`)
    console.log(`Pass Rate: ${((report.totalPassed / report.totalTests) * 100).toFixed(1)}%`)
    console.log(`Duration: ${(report.totalDuration / 1000).toFixed(2)}s`)
    console.log()
    console.log('🎯 Critical Test Status:')
    console.log(`  Symbolic Preservation: ${report.criticalTests.symbolicPreservation ? '✅' : '❌'}`)
    console.log(`  Derivative Accuracy: ${report.criticalTests.derivativeAccuracy ? '✅' : '❌'}`)
    console.log(`  Integration Accuracy: ${report.criticalTests.integrationAccuracy ? '✅' : '❌'}`)
    console.log(`  Limit Accuracy: ${report.criticalTests.limitAccuracy ? '✅' : '❌'}`)
    console.log(`  NLP Integration: ${report.criticalTests.nlpIntegration ? '✅' : '❌'}`)
    console.log(`  Error Handling: ${report.criticalTests.errorHandling ? '✅' : '❌'}`)
    console.log()
    console.log(report.summary)
    console.log('='.repeat(60))
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const runner = new CalculusTestRunner()
  runner.runAllTests()
    .then(report => {
      process.exit(report.totalFailed === 0 ? 0 : 1)
    })
    .catch(error => {
      console.error('❌ Test runner failed:', error)
      process.exit(1)
    })
}

export { CalculusTestRunner }
