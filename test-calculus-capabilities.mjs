// Test current calculus capabilities in math.js
import { derivative, simplify, parse, evaluate } from 'mathjs';

console.log("=== Testing Math.js Calculus Capabilities ===\n");

// Test derivative functionality
console.log("1. Testing Derivative Calculations:");
console.log("==================================");

const derivativeTests = [
  { expr: "x^2", variable: "x", expected: "2*x", description: "Power rule" },
  { expr: "2*x^3 + 3*x^2 + 4*x + 5", variable: "x", expected: "6*x^2 + 6*x + 4", description: "Polynomial" },
  { expr: "sin(x)", variable: "x", expected: "cos(x)", description: "Trigonometric" },
  { expr: "cos(x)", variable: "x", expected: "-sin(x)", description: "Trigonometric" },
  { expr: "e^x", variable: "x", expected: "e^x", description: "Exponential" },
  { expr: "ln(x)", variable: "x", expected: "1/x", description: "Natural logarithm" },
  { expr: "x*sin(x)", variable: "x", expected: "sin(x) + x*cos(x)", description: "Product rule" },
  { expr: "sin(2*x)", variable: "x", expected: "2*cos(2*x)", description: "Chain rule" },
];

let derivativePassed = 0;

for (const test of derivativeTests) {
  try {
    const result = derivative(test.expr, test.variable);
    const simplified = simplify(result);
    console.log(`✓ d/d${test.variable}(${test.expr}) = ${simplified.toString()} (${test.description})`);
    derivativePassed++;
  } catch (error) {
    console.log(`✗ d/d${test.variable}(${test.expr}) = ERROR: ${error.message} (${test.description})`);
  }
}

console.log(`\nDerivative Results: ${derivativePassed}/${derivativeTests.length} passed\n`);

// Test simplification capabilities
console.log("2. Testing Simplification:");
console.log("==========================");

const simplificationTests = [
  { expr: "x + x", expected: "2*x", description: "Basic addition" },
  { expr: "x^2 + 2*x^2", expected: "3*x^2", description: "Like terms" },
  { expr: "sin(x)^2 + cos(x)^2", expected: "1", description: "Trigonometric identity" },
  { expr: "(x+1)*(x-1)", expected: "x^2 - 1", description: "Difference of squares" },
  { expr: "x*y*x", expected: "x^2*y", description: "Rearrangement" },
];

let simplificationPassed = 0;

for (const test of simplificationTests) {
  try {
    const result = simplify(test.expr);
    console.log(`✓ simplify(${test.expr}) = ${result.toString()} (${test.description})`);
    simplificationPassed++;
  } catch (error) {
    console.log(`✗ simplify(${test.expr}) = ERROR: ${error.message} (${test.description})`);
  }
}

console.log(`\nSimplification Results: ${simplificationPassed}/${simplificationTests.length} passed\n`);

// Test expression parsing and evaluation
console.log("3. Testing Expression Parsing:");
console.log("==============================");

const parsingTests = [
  { expr: "x^2 + 3*x + 2", description: "Polynomial expression" },
  { expr: "sin(x) + cos(x)", description: "Trigonometric expression" },
  { expr: "e^(x^2)", description: "Exponential with power" },
  { expr: "ln(x^2 + 1)", description: "Logarithm with expression" },
];

let parsingPassed = 0;

for (const test of parsingTests) {
  try {
    const parsed = parse(test.expr);
    console.log(`✓ parse(${test.expr}) = ${parsed.toString()} (${test.description})`);
    parsingPassed++;
  } catch (error) {
    console.log(`✗ parse(${test.expr}) = ERROR: ${error.message} (${test.description})`);
  }
}

console.log(`\nParsing Results: ${parsingPassed}/${parsingTests.length} passed\n`);

// Test integration capabilities (if available)
console.log("4. Testing Integration Capabilities:");
console.log("====================================");

try {
  // Check if math.js has integration functions
  const mathjs = await import('mathjs');
  
  if (mathjs.integrate) {
    console.log("✓ Integration function available in math.js");
    
    const integrationTests = [
      { expr: "x", variable: "x", description: "Simple polynomial" },
      { expr: "x^2", variable: "x", description: "Power rule" },
      { expr: "sin(x)", variable: "x", description: "Trigonometric" },
    ];
    
    for (const test of integrationTests) {
      try {
        const result = mathjs.integrate(test.expr, test.variable);
        console.log(`✓ ∫(${test.expr})d${test.variable} = ${result.toString()} (${test.description})`);
      } catch (error) {
        console.log(`✗ ∫(${test.expr})d${test.variable} = ERROR: ${error.message} (${test.description})`);
      }
    }
  } else {
    console.log("❌ Integration function not available in math.js");
    console.log("   Need to implement custom integration or use external library");
  }
} catch (error) {
  console.log("❌ Error checking integration capabilities:", error.message);
}

// Test limit capabilities (if available)
console.log("\n5. Testing Limit Capabilities:");
console.log("===============================");

try {
  const mathjs = await import('mathjs');
  
  if (mathjs.limit) {
    console.log("✓ Limit function available in math.js");
  } else {
    console.log("❌ Limit function not available in math.js");
    console.log("   Need to implement custom limit calculation or use external library");
  }
} catch (error) {
  console.log("❌ Error checking limit capabilities:", error.message);
}

// Summary
console.log("\n" + "=".repeat(50));
console.log("CALCULUS CAPABILITIES SUMMARY");
console.log("=".repeat(50));

const totalTests = derivativePassed + simplificationPassed + parsingPassed;
const totalPossible = derivativeTests.length + simplificationTests.length + parsingTests.length;

console.log(`Total Tests Passed: ${totalTests}/${totalPossible} (${Math.round(totalTests/totalPossible*100)}%)`);

console.log("\n✅ Available in Math.js:");
console.log("- Symbolic derivatives (excellent support)");
console.log("- Expression simplification");
console.log("- Expression parsing and evaluation");
console.log("- Symbolic computation");

console.log("\n❌ Missing from Math.js:");
console.log("- Symbolic integration");
console.log("- Limit calculations");
console.log("- Step-by-step solutions");

console.log("\n💡 Recommendations:");
console.log("1. Use math.js for derivatives and simplification");
console.log("2. Add Algebrite for integration and limits");
console.log("3. Implement custom step-by-step solution engine");
console.log("4. Enhance NLP patterns for calculus operations");

console.log("\n🚀 Ready to implement calculus features!");
