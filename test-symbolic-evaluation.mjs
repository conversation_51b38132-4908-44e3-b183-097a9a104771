// Test if math.js results are being evaluated at x=0
import { derivative, simplify, evaluate } from 'mathjs';

console.log("=== Testing Symbolic Evaluation Issue ===\n");

// Test 1: Basic derivative
console.log("1. Basic derivative test:");
const derivResult = derivative("sin(x)", "x");
console.log(`derivative("sin(x)", "x") =`, derivResult);
console.log(`toString() =`, derivResult.toString());
console.log(`type =`, typeof derivResult);

// Test 2: Check if it evaluates to 0 at x=0
console.log("\n2. Evaluation at x=0:");
const evalAtZero = derivResult.evaluate({x: 0});
console.log(`evaluate({x: 0}) =`, evalAtZero);

// Test 3: Check if it evaluates to 1 at x=π/2
console.log("\n3. Evaluation at x=π/2:");
const evalAtPiHalf = derivResult.evaluate({x: Math.PI/2});
console.log(`evaluate({x: π/2}) =`, evalAtPiHalf);

// Test 4: Check if valueOf() returns a number
console.log("\n4. Check valueOf():");
try {
  const valueOfResult = derivResult.valueOf();
  console.log(`valueOf() =`, valueOfResult);
  console.log(`valueOf() type =`, typeof valueOfResult);
} catch (error) {
  console.log(`valueOf() error:`, error.message);
}

// Test 5: Check if JSON.stringify causes evaluation
console.log("\n5. Check JSON.stringify:");
try {
  const jsonResult = JSON.stringify(derivResult);
  console.log(`JSON.stringify() =`, jsonResult);
} catch (error) {
  console.log(`JSON.stringify() error:`, error.message);
}

// Test 6: Check if the result has a default evaluation
console.log("\n6. Check default evaluation:");
console.log(`derivResult.value =`, derivResult.value);
console.log(`derivResult.entries =`, derivResult.entries);

// Test 7: Test x^2 derivative
console.log("\n7. Test x^2 derivative:");
const derivResult2 = derivative("x^2", "x");
console.log(`derivative("x^2", "x") =`, derivResult2.toString());
console.log(`evaluate({x: 0}) =`, derivResult2.evaluate({x: 0}));
console.log(`evaluate({x: 1}) =`, derivResult2.evaluate({x: 1}));

// Test 8: Check if the issue is in the simplify step
console.log("\n8. Test simplify step:");
const simplified = simplify(derivResult);
console.log(`simplify(cos(x)) =`, simplified.toString());
console.log(`simplified evaluate({x: 0}) =`, simplified.evaluate({x: 0}));

// Test 9: Check if the issue is in string conversion
console.log("\n9. Test string conversion:");
const resultString = simplified.toString();
console.log(`toString() result =`, resultString);
console.log(`typeof toString() =`, typeof resultString);

// Test 10: Check if evaluate() is being called somewhere
console.log("\n10. Test if evaluate is called without parameters:");
try {
  const evalWithoutParams = evaluate(resultString);
  console.log(`evaluate("${resultString}") =`, evalWithoutParams);
  console.log(`type =`, typeof evalWithoutParams);
} catch (error) {
  console.log(`evaluate("${resultString}") error:`, error.message);
}

console.log("\n=== Test Complete ===");
