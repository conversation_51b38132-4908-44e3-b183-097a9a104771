import { describe, it, expect, beforeEach } from 'vitest'
import { calculusEngine } from '@/lib/calculus-engine'
import { mathEngine } from '@/lib/math-engine'
import { nlpProcessor } from '@/lib/nlp-processor'

describe('Derivative Operations', () => {
  describe('Basic Derivatives', () => {
    it('should calculate derivative of sin(x) correctly', async () => {
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      expect(result.result).toBe('cos(x)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
      expect(result.originalExpression).toBe('sin(x)')
    })

    it('should calculate derivative of cos(x) correctly', async () => {
      const result = await calculusEngine.calculateDerivative('cos(x)', 'x')
      
      expect(result.result).toBe('-sin(x)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of x^2 correctly', async () => {
      const result = await calculusEngine.calculateDerivative('x^2', 'x')
      
      expect(result.result).toBe('2 * x')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of x^3 correctly', async () => {
      const result = await calculusEngine.calculateDerivative('x^3', 'x')

      expect(result.result).toBe('3 * x ^ 2')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of e^x correctly', async () => {
      const result = await calculusEngine.calculateDerivative('e^x', 'x')

      expect(result.result).toBe('e ^ x')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of log(x) correctly', async () => {
      const result = await calculusEngine.calculateDerivative('log(x)', 'x')

      expect(result.result).toBe('1 / x')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of tan(x) correctly', async () => {
      const result = await calculusEngine.calculateDerivative('tan(x)', 'x')

      expect(result.result).toBe('sec(x) ^ 2')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Polynomial Derivatives', () => {
    it('should calculate derivative of polynomial x^3 + 2x^2 - 5x + 1', async () => {
      const result = await calculusEngine.calculateDerivative('x^3 + 2*x^2 - 5*x + 1', 'x')

      expect(result.result).toBe('3 * x ^ 2 + 4 * x - 5')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of 5x^4 - 3x^3 + 2x - 7', async () => {
      const result = await calculusEngine.calculateDerivative('5*x^4 - 3*x^3 + 2*x - 7', 'x')

      // Math.js may reorder terms, so we'll check for the actual output format
      expect(result.result).toMatch(/20 \* x \^ 3.*2.*9 \* x \^ 2/)
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of constant', async () => {
      const result = await calculusEngine.calculateDerivative('5', 'x')
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of linear function', async () => {
      const result = await calculusEngine.calculateDerivative('3*x + 7', 'x')
      
      expect(result.result).toBe('3')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Chain Rule Applications', () => {
    it('should calculate derivative of sin(2x) using chain rule', async () => {
      const result = await calculusEngine.calculateDerivative('sin(2*x)', 'x')
      
      expect(result.result).toBe('2 * cos(2 * x)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of cos(3x)', async () => {
      const result = await calculusEngine.calculateDerivative('cos(3*x)', 'x')

      expect(result.result).toBe('-(3 * sin(3 * x))')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of (x^2 + 1)^3', async () => {
      const result = await calculusEngine.calculateDerivative('(x^2 + 1)^3', 'x')
      
      // The exact form may vary, but should contain the chain rule application
      expect(result.result).toContain('x')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of sqrt(x^2 + 1)', async () => {
      const result = await calculusEngine.calculateDerivative('sqrt(x^2 + 1)', 'x')
      
      // The exact form may vary, but should contain the chain rule application
      expect(result.result).toContain('x')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Product Rule Applications', () => {
    it('should calculate derivative of x*sin(x) using product rule', async () => {
      const result = await calculusEngine.calculateDerivative('x*sin(x)', 'x')
      
      expect(result.result).toBe('sin(x) + x * cos(x)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of x^2*cos(x)', async () => {
      const result = await calculusEngine.calculateDerivative('x^2*cos(x)', 'x')

      expect(result.result).toBe('2 * x * cos(x) - x ^ 2 * sin(x)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })

    it('should calculate derivative of x*e^x', async () => {
      const result = await calculusEngine.calculateDerivative('x*e^x', 'x')

      expect(result.result).toBe('e ^ x * (x + 1)')
      expect(result.operation).toBe('derivative')
      expect(result.variable).toBe('x')
      expect(result.isExact).toBe(true)
    })
  })

  describe('Natural Language Input Processing', () => {
    it('should process "derivative of sin(x)" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('derivative of sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('sin(x)')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toBe('cos(x)')
    })

    it('should process "differentiate x^2" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('differentiate x^2')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('x^2')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toBe('2 * x')
    })

    it('should process "d/dx(cos(x))" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('d/dx(cos(x))')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('cos(x)')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toBe('-sin(x)')
    })

    it('should process "what is the derivative of x^3" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('what is the derivative of x^3')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('x^3')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toBe('3 * x^2')
    })

    it('should process "derive x*sin(x)" correctly', async () => {
      const intent = nlpProcessor.parseNaturalLanguage('derive x*sin(x)')
      const result = await mathEngine.processIntent(intent)
      
      expect(intent.operation).toBe('differentiate')
      expect(intent.expression).toBe('x*sin(x)')
      expect(intent.variables).toEqual(['x'])
      expect(result.formattedValue).toBe('sin(x) + x * cos(x)')
    })
  })

  describe('Step-by-Step Solutions', () => {
    it('should provide step-by-step solution for basic derivative', async () => {
      const result = await calculusEngine.calculateDerivative('x^2', 'x')
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('derivative')
      expect(result.steps.some(step => step.rule === 'Power Rule')).toBe(true)
    })

    it('should provide step-by-step solution for trigonometric derivative', async () => {
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('derivative')
      expect(result.steps.some(step => step.rule === 'Trigonometric Rules')).toBe(true)
    })

    it('should provide step-by-step solution for product rule', async () => {
      const result = await calculusEngine.calculateDerivative('x*sin(x)', 'x')
      
      expect(result.steps).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
      expect(result.steps[0].description).toContain('derivative')
      expect(result.steps.some(step => step.rule === 'Product Rule')).toBe(true)
    })
  })

  describe('Alternative Formats', () => {
    it('should provide alternative formats for derivative results', async () => {
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      expect(result.alternativeFormats).toBeDefined()
      expect(result.alternativeFormats).toContain('cos(x)')
      expect(result.alternativeFormats).toContain('d/dx(sin(x))')
      expect(result.alternativeFormats).toContain("f'(x) = cos(x)")
    })
  })
})
