import { describe, it, expect } from 'vitest'
import { calculusEngine } from '@/lib/calculus-engine'
import { mathEngine } from '@/lib/math-engine'
import { nlpProcessor } from '@/lib/nlp-processor'

describe('Calculus Error Handling', () => {
  describe('Invalid Expression Handling', () => {
    it('should handle invalid mathematical expressions gracefully', async () => {
      const invalidExpressions = [
        'sin(x',           // Missing closing parenthesis
        'x^',              // Incomplete power
        'cos)',            // Missing opening parenthesis
        '2x + 3y z',       // Invalid syntax
        'sqrt(-x^2)',      // Complex domain issues
      ]

      for (const expr of invalidExpressions) {
        await expect(async () => {
          await calculusEngine.calculateDerivative(expr, 'x')
        }).rejects.toThrow()
      }
    })

    it('should handle empty expressions', async () => {
      await expect(async () => {
        await calculusEngine.calculateDerivative('', 'x')
      }).rejects.toThrow()

      await expect(async () => {
        await calculusEngine.calculateIntegral('', 'x')
      }).rejects.toThrow()

      await expect(async () => {
        await calculusEngine.calculateLimit('', { variable: 'x', approaches: '0' })
      }).rejects.toThrow()
    })

    it('should handle whitespace-only expressions', async () => {
      await expect(async () => {
        await calculusEngine.calculateDerivative('   ', 'x')
      }).rejects.toThrow()

      await expect(async () => {
        await calculusEngine.calculateIntegral('   ', 'x')
      }).rejects.toThrow()
    })

    it('should handle expressions with undefined functions', async () => {
      const undefinedFunctions = [
        'unknownFunc(x)',
        'myFunction(x)',
        'customFunc(x^2)',
      ]

      for (const expr of undefinedFunctions) {
        await expect(async () => {
          await calculusEngine.calculateDerivative(expr, 'x')
        }).rejects.toThrow()
      }
    })
  })

  describe('Variable Validation', () => {
    it('should handle invalid variable names', async () => {
      const invalidVariables = [
        '',           // Empty variable
        '123',        // Number as variable
        'sin',        // Function name as variable
        'π',          // Special character
        'x y',        // Space in variable
      ]

      for (const variable of invalidVariables) {
        await expect(async () => {
          await calculusEngine.calculateDerivative('x^2', variable)
        }).rejects.toThrow()
      }
    })

    it('should handle expressions without the specified variable', async () => {
      // These should return 0 for derivatives (constant rule)
      const result1 = await calculusEngine.calculateDerivative('5', 'x')
      expect(result1.result).toBe('0')

      const result2 = await calculusEngine.calculateDerivative('y^2', 'x')
      expect(result2.result).toBe('0')

      // For integrals, should treat as constant
      const result3 = await calculusEngine.calculateIntegral('5', 'x')
      expect(result3.result).toContain('5*x')
      expect(result3.result).toContain('+ C')
    })

    it('should validate variable presence in expressions', async () => {
      const validation = calculusEngine.validateCalculusExpression('y^2 + z', 'x')
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toContain("Variable 'x' not found in expression")
    })
  })

  describe('Limit Boundary Conditions', () => {
    it('should handle invalid limit approaches values', async () => {
      const invalidApproaches = [
        '',           // Empty
        'abc',        // Non-numeric/non-infinity
        'undefined',  // Invalid keyword
        '1/0',        // Division by zero
      ]

      for (const approaches of invalidApproaches) {
        await expect(async () => {
          await calculusEngine.calculateLimit('x', { variable: 'x', approaches })
        }).rejects.toThrow()
      }
    })

    it('should handle limits that do not exist', async () => {
      // Test limits that oscillate or don't exist
      const result = await calculusEngine.calculateLimit('1/x', { variable: 'x', approaches: '0' })
      
      // Should handle gracefully, might return 'undefined' or throw
      expect(typeof result.result).toBe('string')
    })

    it('should handle limits at discontinuities', async () => {
      // Test functions with discontinuities
      const result = await calculusEngine.calculateLimit('abs(x)/x', { variable: 'x', approaches: '0' })
      
      // Should handle gracefully
      expect(typeof result.result).toBe('string')
    })
  })

  describe('Definite Integral Boundary Validation', () => {
    it('should handle invalid integration bounds', async () => {
      const invalidBounds = [
        { lower: '', upper: '1' },
        { lower: '0', upper: '' },
        { lower: 'abc', upper: '1' },
        { lower: '0', upper: 'xyz' },
        { lower: '1', upper: '0' },  // Upper < Lower
      ]

      for (const bounds of invalidBounds) {
        await expect(async () => {
          await calculusEngine.calculateIntegral('x', 'x', bounds)
        }).rejects.toThrow()
      }
    })

    it('should handle equal integration bounds', async () => {
      const result = await calculusEngine.calculateIntegral('x^2', 'x', { lower: '1', upper: '1' })
      
      expect(result.result).toBe('0')
      expect(result.operation).toBe('integral')
    })

    it('should handle infinite bounds', async () => {
      // Test integration to infinity
      const result = await calculusEngine.calculateIntegral('1/x^2', 'x', { lower: '1', upper: 'infinity' })
      
      expect(result.result).toBe('1')
      expect(result.operation).toBe('integral')
    })
  })

  describe('Unsupported Operations', () => {
    it('should handle unsupported functions gracefully', async () => {
      const unsupportedFunctions = [
        'factorial(x)',
        'gamma(x)',
        'beta(x, y)',
        'zeta(x)',
      ]

      for (const func of unsupportedFunctions) {
        const validation = calculusEngine.validateCalculusExpression(func, 'x')
        
        expect(validation.isValid).toBe(false)
        expect(validation.errors.some(error => error.includes('not supported'))).toBe(true)
      }
    })

    it('should provide helpful error messages for unsupported operations', async () => {
      await expect(async () => {
        await calculusEngine.calculateDerivative('factorial(x)', 'x')
      }).rejects.toThrow(/not supported/)
    })
  })

  describe('NLP Error Handling', () => {
    it('should handle ambiguous natural language input', async () => {
      const ambiguousInputs = [
        'derivative',                    // Missing expression
        'integral of',                   // Incomplete
        'limit as x approaches',         // Missing value
        'find the thing of x',          // Unclear operation
        'calculate something',           // Vague request
      ]

      for (const input of ambiguousInputs) {
        expect(() => {
          nlpProcessor.parseNaturalLanguage(input)
        }).toThrow()
      }
    })

    it('should handle malformed mathematical notation', async () => {
      const malformedInputs = [
        'd/d(sin(x))',                  // Missing variable
        'integral of x d',              // Incomplete differential
        'limit of x as approaches 0',   // Missing variable
        '∫ sin(x)',                     // Missing differential
      ]

      for (const input of malformedInputs) {
        expect(() => {
          nlpProcessor.parseNaturalLanguage(input)
        }).toThrow()
      }
    })

    it('should provide meaningful error messages for NLP failures', async () => {
      try {
        nlpProcessor.parseNaturalLanguage('derivative')
      } catch (error) {
        expect(error.message).toContain('expression')
        expect(error.message.length).toBeGreaterThan(10) // Should be descriptive
      }
    })
  })

  describe('Edge Cases', () => {
    it('should handle very large numbers', async () => {
      const result = await calculusEngine.calculateDerivative('x^1000', 'x')
      
      expect(result.result).toContain('1000')
      expect(result.result).toContain('x^999')
      expect(result.operation).toBe('derivative')
    })

    it('should handle very small coefficients', async () => {
      const result = await calculusEngine.calculateDerivative('0.000001*x^2', 'x')
      
      expect(result.result).toContain('0.000002')
      expect(result.result).toContain('x')
      expect(result.operation).toBe('derivative')
    })

    it('should handle complex nested expressions', async () => {
      const result = await calculusEngine.calculateDerivative('sin(cos(tan(x)))', 'x')
      
      expect(result.result).toContain('cos')
      expect(result.result).toContain('sin')
      expect(result.operation).toBe('derivative')
      expect(result.isExact).toBe(true)
    })

    it('should handle expressions with multiple variables', async () => {
      // Should treat other variables as constants
      const result = await calculusEngine.calculateDerivative('x*y + z^2', 'x')
      
      expect(result.result).toBe('y')
      expect(result.operation).toBe('derivative')
    })
  })

  describe('Performance and Timeout Handling', () => {
    it('should handle computationally intensive expressions', async () => {
      // Test with a complex expression that might take time
      const complexExpr = 'sin(x)*cos(x)*tan(x)*sec(x)*csc(x)*cot(x)'
      
      const startTime = Date.now()
      const result = await calculusEngine.calculateDerivative(complexExpr, 'x')
      const endTime = Date.now()
      
      expect(result.operation).toBe('derivative')
      expect(endTime - startTime).toBeLessThan(5000) // Should complete within 5 seconds
    })

    it('should handle deeply nested expressions', async () => {
      const nestedExpr = 'sin(sin(sin(sin(x))))'
      
      const result = await calculusEngine.calculateDerivative(nestedExpr, 'x')
      
      expect(result.operation).toBe('derivative')
      expect(result.result).toContain('cos')
    })
  })

  describe('Recovery and Fallback', () => {
    it('should provide fallback when Algebrite is unavailable', async () => {
      // This tests the graceful degradation when external libraries fail
      // The test might need to mock Algebrite failure
      
      const result = await calculusEngine.calculateDerivative('sin(x)', 'x')
      
      // Should still work with math.js for derivatives
      expect(result.result).toBe('cos(x)')
      expect(result.operation).toBe('derivative')
    })

    it('should provide helpful suggestions for common mistakes', async () => {
      try {
        await calculusEngine.calculateDerivative('sin x', 'x') // Missing parentheses
      } catch (error) {
        expect(error.message).toContain('parentheses')
      }
    })

    it('should handle partial failures gracefully', async () => {
      // Test when step generation fails but calculation succeeds
      const result = await calculusEngine.calculateDerivative('x^2', 'x')
      
      expect(result.result).toBe('2 * x')
      expect(result.steps).toBeDefined()
      expect(Array.isArray(result.steps)).toBe(true)
    })
  })
})
