declare module 'algebrite' {
  interface AlgebriteResult {
    toString(): string
  }

  export function run(expression: string): string
  export function eval(expression: string): AlgebriteResult
  export function integral(expression: AlgebriteResult, variable: AlgebriteResult): AlgebriteResult
  export function derivative(expression: AlgebriteResult, variable: AlgebriteResult): AlgebriteResult
  export function limit(expression: AlgebriteResult, variable: AlgebriteResult, value: AlgebriteResult): AlgebriteResult
  export function factor(expression: string): AlgebriteResult
  export function simplify(expression: AlgebriteResult): AlgebriteResult
}
